import { ImageService, ImageGenerationParams, ImageResult, ImageServiceConfig } from './ImageService'
import { OpenAIImageService } from './OpenAIImageService'
import { ConfigService } from '../ConfigService'
import { DatabaseService } from '../../database/DatabaseService'
import { promises as fs } from 'fs'
import { join } from 'path'
import { app } from 'electron'

export type ImageProvider = 'openai' | 'gemini'

export interface ImageGenerationRequest extends Omit<ImageGenerationParams, 'model'> {
  provider?: ImageProvider
  model?: string
  userId?: number
}

export interface StoredImageResult extends ImageResult {
  localPath?: string
  prompt: string
  provider: string
  userId: number
  metadata?: any
}

export class ImageManager {
  private static instance: ImageManager
  private services: Map<ImageProvider, ImageService> = new Map()
  private config: ConfigService
  private db: DatabaseService
  private defaultProvider: ImageProvider = 'openai'
  private imagesDir: string

  private constructor() {
    this.config = ConfigService.getInstance()
    this.db = DatabaseService.getInstance()
    this.imagesDir = join(app.getPath('userData'), 'images')
    this.initializeServices()
    this.ensureImagesDirectory()
  }

  public static getInstance(): ImageManager {
    if (!ImageManager.instance) {
      ImageManager.instance = new ImageManager()
    }
    return ImageManager.instance
  }

  private async ensureImagesDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.imagesDir, { recursive: true })
    } catch (error) {
      console.error('Failed to create images directory:', error)
    }
  }

  private async initializeServices(): Promise<void> {
    try {
      // Load default provider
      const savedProvider = await this.config.get<ImageProvider>('image.defaultProvider')
      if (savedProvider) {
        this.defaultProvider = savedProvider
      }

      // Initialize OpenAI service
      const openaiConfig = await this.getServiceConfig('openai')
      if (openaiConfig.apiKey) {
        this.services.set('openai', new OpenAIImageService(openaiConfig))
      }

      // TODO: Add other image services (Gemini, etc.)

      console.log(`✅ Image Manager initialized with ${this.services.size} services`)
    } catch (error) {
      console.error('Failed to initialize image services:', error)
    }
  }

  private async getServiceConfig(provider: ImageProvider): Promise<ImageServiceConfig> {
    const apiKey = await this.config.get<string>(`ai.${provider}.apiKey`) || ''
    const model = await this.config.get<string>(`image.${provider}.model`)
    const defaultSize = await this.config.get<string>('image.defaultSize')
    const defaultQuality = await this.config.get<'standard' | 'hd'>('image.defaultQuality')

    return {
      apiKey,
      model,
      defaultSize,
      defaultQuality
    }
  }

  public async generateImage(params: ImageGenerationRequest): Promise<StoredImageResult> {
    const provider = params.provider || this.defaultProvider
    const service = this.services.get(provider)

    if (!service) {
      throw new Error(`Image service '${provider}' is not available. Please check your configuration.`)
    }

    try {
      // Generate image
      const result = await service.generateImage({
        ...params,
        model: params.model
      })

      // Download and store image locally
      const localPath = await this.downloadAndStoreImage(result.url, result.id)

      // Create stored result
      const storedResult: StoredImageResult = {
        ...result,
        localPath,
        prompt: params.prompt,
        provider,
        userId: params.userId || 1
      }

      // Save to database
      await this.saveImageRecord(storedResult)

      return storedResult
    } catch (error) {
      console.error('Image generation failed:', error)
      throw error
    }
  }

  private async downloadAndStoreImage(url: string, imageId: string): Promise<string> {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.statusText}`)
      }

      const buffer = await response.arrayBuffer()
      const filename = `${imageId}.png`
      const localPath = join(this.imagesDir, filename)

      await fs.writeFile(localPath, Buffer.from(buffer))
      return localPath
    } catch (error) {
      console.error('Failed to download and store image:', error)
      throw new Error('Failed to save image locally')
    }
  }

  private async saveImageRecord(result: StoredImageResult): Promise<void> {
    try {
      await this.db.run(
        `INSERT INTO image_generations 
         (id, user_id, prompt, model, image_url, metadata) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          result.id,
          result.userId,
          result.prompt,
          `${result.provider}:${result.model}`,
          result.localPath || result.url,
          JSON.stringify({
            provider: result.provider,
            size: result.size,
            revisedPrompt: result.revisedPrompt,
            originalUrl: result.url,
            created: result.created
          })
        ]
      )
    } catch (error) {
      console.error('Failed to save image record:', error)
      throw new Error('Failed to save image record')
    }
  }

  public async getImageHistory(userId: number, limit: number = 50, offset: number = 0): Promise<StoredImageResult[]> {
    try {
      const records = await this.db.all(
        `SELECT * FROM image_generations 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT ? OFFSET ?`,
        [userId, limit, offset]
      )

      return records.map(record => {
        const metadata = record.metadata ? JSON.parse(record.metadata) : {}
        return {
          id: record.id,
          url: record.image_url,
          localPath: record.image_url,
          prompt: record.prompt,
          provider: metadata.provider || 'unknown',
          userId: record.user_id,
          model: record.model,
          size: metadata.size || 'unknown',
          revisedPrompt: metadata.revisedPrompt,
          created: metadata.created || 0,
          metadata
        }
      })
    } catch (error) {
      console.error('Failed to get image history:', error)
      return []
    }
  }

  public async deleteImage(imageId: string): Promise<boolean> {
    try {
      // Get image record
      const record = await this.db.get(
        'SELECT * FROM image_generations WHERE id = ?',
        [imageId]
      )

      if (!record) return false

      // Delete local file if exists
      if (record.image_url && record.image_url.startsWith('/')) {
        try {
          await fs.unlink(record.image_url)
        } catch (error) {
          console.warn('Failed to delete local image file:', error)
        }
      }

      // Delete database record
      const result = await this.db.run(
        'DELETE FROM image_generations WHERE id = ?',
        [imageId]
      )

      return result.changes > 0
    } catch (error) {
      console.error('Failed to delete image:', error)
      return false
    }
  }

  public getAvailableProviders(): ImageProvider[] {
    return Array.from(this.services.keys())
  }

  public async getSupportedModels(provider?: ImageProvider): Promise<{ provider: ImageProvider; models: string[] }[]> {
    const results: { provider: ImageProvider; models: string[] }[] = []

    if (provider) {
      const service = this.services.get(provider)
      if (service) {
        results.push({ provider, models: service.getSupportedModels() })
      }
    } else {
      for (const [providerName, service] of this.services.entries()) {
        results.push({ provider: providerName, models: service.getSupportedModels() })
      }
    }

    return results
  }

  public async getSupportedSizes(provider?: ImageProvider): Promise<{ provider: ImageProvider; sizes: string[] }[]> {
    const results: { provider: ImageProvider; sizes: string[] }[] = []

    if (provider) {
      const service = this.services.get(provider)
      if (service) {
        results.push({ provider, sizes: service.getSupportedSizes() })
      }
    } else {
      for (const [providerName, service] of this.services.entries()) {
        results.push({ provider: providerName, sizes: service.getSupportedSizes() })
      }
    }

    return results
  }

  public async setDefaultProvider(provider: ImageProvider): Promise<void> {
    if (!this.services.has(provider)) {
      throw new Error(`Provider '${provider}' is not available`)
    }

    this.defaultProvider = provider
    await this.config.set('image.defaultProvider', provider)
  }

  public getDefaultProvider(): ImageProvider {
    return this.defaultProvider
  }

  public async validateService(provider: ImageProvider): Promise<boolean> {
    const service = this.services.get(provider)
    if (!service) return false

    try {
      return await service.validateConfig()
    } catch (error) {
      console.error(`Image service validation failed for ${provider}:`, error)
      return false
    }
  }
}