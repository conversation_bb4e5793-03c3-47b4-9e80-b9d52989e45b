import { EventEmitter } from 'events'

export interface WorkflowStep {
  id: string
  type: 'search' | 'analyze' | 'synthesize' | 'generate'
  name: string
  config: any
  dependencies?: string[]
}

export interface Workflow {
  id: string
  name: string
  description: string
  steps: WorkflowStep[]
  timeout?: number
}

export interface ExecuteOptions {
  input: any
  context?: any
  onProgress?: (step: string, progress: number, message: string) => void
  onStepComplete?: (stepId: string, result: any) => void
}

export interface WorkflowContext {
  input: any
  results: Map<string, any>
  metadata: any
}

export abstract class StepProcessor {
  abstract execute(step: WorkflowStep, context: WorkflowContext): Promise<any>
  abstract getType(): string
}

export class SearchStepProcessor extends StepProcessor {
  private searchManager: any

  constructor(searchManager: any) {
    super()
    this.searchManager = searchManager
  }

  async execute(step: WorkflowStep, context: WorkflowContext): Promise<any> {
    const { query, maxResults = 10 } = step.config
    const searchQuery = this.interpolateString(query, context)
    
    const results = await this.searchManager.search(searchQuery, { maxResults })
    return { query: searchQuery, results }
  }

  getType(): string {
    return 'search'
  }

  private interpolateString(template: string, context: WorkflowContext): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return context.input[key] || context.results.get(key) || match
    })
  }
}

export class AnalyzeStepProcessor extends StepProcessor {
  private aiManager: any

  constructor(aiManager: any) {
    super()
    this.aiManager = aiManager
  }

  async execute(step: WorkflowStep, context: WorkflowContext): Promise<any> {
    const { prompt, inputStep } = step.config
    const inputData = context.results.get(inputStep)
    
    if (!inputData) {
      throw new Error(`Input step '${inputStep}' not found`)
    }

    const analysisPrompt = this.buildAnalysisPrompt(prompt, inputData, context)
    
    const response = await this.aiManager.chatCompletion({
      messages: [
        { role: 'system', content: 'You are a research analyst. Analyze the provided information and extract key insights.' },
        { role: 'user', content: analysisPrompt }
      ]
    })

    return {
      analysis: response.content,
      inputData,
      metadata: { model: response.model, usage: response.usage }
    }
  }

  getType(): string {
    return 'analyze'
  }

  private buildAnalysisPrompt(template: string, data: any, context: WorkflowContext): string {
    let prompt = template
    
    // Replace placeholders
    prompt = prompt.replace(/\{\{input\}\}/g, JSON.stringify(data, null, 2))
    prompt = prompt.replace(/\{\{query\}\}/g, context.input.query || '')
    
    return prompt
  }
}

export class SynthesizeStepProcessor extends StepProcessor {
  private aiManager: any

  constructor(aiManager: any) {
    super()
    this.aiManager = aiManager
  }

  async execute(step: WorkflowStep, context: WorkflowContext): Promise<any> {
    const { prompt, inputSteps } = step.config
    const inputData: any[] = []
    
    for (const stepId of inputSteps) {
      const data = context.results.get(stepId)
      if (data) {
        inputData.push(data)
      }
    }

    const synthesisPrompt = this.buildSynthesisPrompt(prompt, inputData, context)
    
    const response = await this.aiManager.chatCompletion({
      messages: [
        { role: 'system', content: 'You are a research synthesizer. Combine and synthesize information from multiple sources into coherent insights.' },
        { role: 'user', content: synthesisPrompt }
      ]
    })

    return {
      synthesis: response.content,
      inputData,
      metadata: { model: response.model, usage: response.usage }
    }
  }

  getType(): string {
    return 'synthesize'
  }

  private buildSynthesisPrompt(template: string, data: any[], context: WorkflowContext): string {
    let prompt = template
    
    // Replace placeholders
    prompt = prompt.replace(/\{\{inputs\}\}/g, JSON.stringify(data, null, 2))
    prompt = prompt.replace(/\{\{query\}\}/g, context.input.query || '')
    
    return prompt
  }
}

export class GenerateStepProcessor extends StepProcessor {
  private aiManager: any

  constructor(aiManager: any) {
    super()
    this.aiManager = aiManager
  }

  async execute(step: WorkflowStep, context: WorkflowContext): Promise<any> {
    const { prompt, format = 'markdown', inputStep } = step.config
    const inputData = context.results.get(inputStep)
    
    if (!inputData) {
      throw new Error(`Input step '${inputStep}' not found`)
    }

    const generationPrompt = this.buildGenerationPrompt(prompt, inputData, context, format)
    
    const response = await this.aiManager.chatCompletion({
      messages: [
        { role: 'system', content: `You are a research report generator. Generate a comprehensive ${format} report based on the provided research data.` },
        { role: 'user', content: generationPrompt }
      ]
    })

    return {
      report: response.content,
      format,
      inputData,
      metadata: { model: response.model, usage: response.usage }
    }
  }

  getType(): string {
    return 'generate'
  }

  private buildGenerationPrompt(template: string, data: any, context: WorkflowContext, format: string): string {
    let prompt = template
    
    // Replace placeholders
    prompt = prompt.replace(/\{\{data\}\}/g, JSON.stringify(data, null, 2))
    prompt = prompt.replace(/\{\{query\}\}/g, context.input.query || '')
    prompt = prompt.replace(/\{\{format\}\}/g, format)
    
    return prompt
  }
}

export class WorkflowEngine extends EventEmitter {
  private processors: Map<string, StepProcessor> = new Map()

  constructor() {
    super()
  }

  public registerProcessor(processor: StepProcessor): void {
    this.processors.set(processor.getType(), processor)
  }

  public async execute(workflow: Workflow, options: ExecuteOptions): Promise<any> {
    const context: WorkflowContext = {
      input: options.input,
      results: new Map(),
      metadata: {
        workflowId: workflow.id,
        startTime: Date.now(),
        steps: workflow.steps.length
      }
    }

    try {
      const totalSteps = workflow.steps.length
      let completedSteps = 0

      // Execute steps in dependency order
      const executionOrder = this.resolveDependencies(workflow.steps)
      
      for (const step of executionOrder) {
        options.onProgress?.(step.name, (completedSteps / totalSteps) * 100, `Executing: ${step.name}`)
        
        const processor = this.processors.get(step.type)
        if (!processor) {
          throw new Error(`No processor found for step type: ${step.type}`)
        }

        const stepStartTime = Date.now()
        const result = await processor.execute(step, context)
        const stepDuration = Date.now() - stepStartTime

        context.results.set(step.id, result)
        completedSteps++

        options.onStepComplete?.(step.id, result)
        this.emit('stepComplete', { stepId: step.id, result, duration: stepDuration })
      }

      context.metadata.endTime = Date.now()
      context.metadata.duration = context.metadata.endTime - context.metadata.startTime

      options.onProgress?.('Complete', 100, 'Workflow completed successfully')
      
      return {
        success: true,
        results: Object.fromEntries(context.results),
        metadata: context.metadata
      }
    } catch (error) {
      this.emit('error', { workflowId: workflow.id, error })
      throw error
    }
  }

  private resolveDependencies(steps: WorkflowStep[]): WorkflowStep[] {
    const resolved: WorkflowStep[] = []
    const remaining = [...steps]
    const resolvedIds = new Set<string>()

    while (remaining.length > 0) {
      const canExecute = remaining.filter(step => 
        !step.dependencies || step.dependencies.every(dep => resolvedIds.has(dep))
      )

      if (canExecute.length === 0) {
        throw new Error('Circular dependency detected in workflow steps')
      }

      for (const step of canExecute) {
        resolved.push(step)
        resolvedIds.add(step.id)
        remaining.splice(remaining.indexOf(step), 1)
      }
    }

    return resolved
  }

  public validateWorkflow(workflow: Workflow): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check for duplicate step IDs
    const stepIds = workflow.steps.map(s => s.id)
    const duplicates = stepIds.filter((id, index) => stepIds.indexOf(id) !== index)
    if (duplicates.length > 0) {
      errors.push(`Duplicate step IDs: ${duplicates.join(', ')}`)
    }

    // Check dependencies exist
    for (const step of workflow.steps) {
      if (step.dependencies) {
        for (const dep of step.dependencies) {
          if (!stepIds.includes(dep)) {
            errors.push(`Step '${step.id}' depends on non-existent step '${dep}'`)
          }
        }
      }
    }

    // Check processors exist
    for (const step of workflow.steps) {
      if (!this.processors.has(step.type)) {
        errors.push(`No processor available for step type '${step.type}'`)
      }
    }

    return { valid: errors.length === 0, errors }
  }
}