import { join } from 'path'
import { app } from 'electron'
import { promises as fs } from 'fs'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: string
  metadata?: any
  stack?: string
}

export class LoggerService {
  private static instance: LoggerService
  private logLevel: LogLevel = LogLevel.INFO
  private logDir: string
  private logFile: string
  private maxLogSize: number = 10 * 1024 * 1024 // 10MB
  private maxLogFiles: number = 5

  private constructor() {
    this.logDir = join(app.getPath('userData'), 'logs')
    this.logFile = join(this.logDir, 'app.log')
    this.ensureLogDirectory()
  }

  public static getInstance(): LoggerService {
    if (!LoggerService.instance) {
      LoggerService.instance = new LoggerService()
    }
    return LoggerService.instance
  }

  private async ensureLogDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.logDir, { recursive: true })
    } catch (error) {
      console.error('Failed to create log directory:', error)
    }
  }

  public setLogLevel(level: LogLevel): void {
    this.logLevel = level
  }

  public debug(message: string, context?: string, metadata?: any): void {
    this.log(LogLevel.DEBUG, message, context, metadata)
  }

  public info(message: string, context?: string, metadata?: any): void {
    this.log(LogLevel.INFO, message, context, metadata)
  }

  public warn(message: string, context?: string, metadata?: any): void {
    this.log(LogLevel.WARN, message, context, metadata)
  }

  public error(message: string, context?: string, metadata?: any, error?: Error): void {
    this.log(LogLevel.ERROR, message, context, metadata, error?.stack)
  }

  public fatal(message: string, context?: string, metadata?: any, error?: Error): void {
    this.log(LogLevel.FATAL, message, context, metadata, error?.stack)
  }

  private async log(level: LogLevel, message: string, context?: string, metadata?: any, stack?: string): Promise<void> {
    if (level < this.logLevel) return

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      metadata,
      stack
    }

    // Console output
    this.logToConsole(entry)

    // File output
    await this.logToFile(entry)
  }

  private logToConsole(entry: LogEntry): void {
    const levelName = LogLevel[entry.level]
    const contextStr = entry.context ? `[${entry.context}]` : ''
    const message = `${entry.timestamp} ${levelName} ${contextStr} ${entry.message}`

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.metadata)
        break
      case LogLevel.INFO:
        console.info(message, entry.metadata)
        break
      case LogLevel.WARN:
        console.warn(message, entry.metadata)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(message, entry.metadata)
        if (entry.stack) console.error(entry.stack)
        break
    }
  }

  private async logToFile(entry: LogEntry): Promise<void> {
    try {
      const logLine = JSON.stringify(entry) + '\n'
      
      // Check file size and rotate if necessary
      await this.rotateLogIfNeeded()
      
      await fs.appendFile(this.logFile, logLine)
    } catch (error) {
      console.error('Failed to write to log file:', error)
    }
  }

  private async rotateLogIfNeeded(): Promise<void> {
    try {
      const stats = await fs.stat(this.logFile)
      
      if (stats.size >= this.maxLogSize) {
        // Rotate logs
        for (let i = this.maxLogFiles - 1; i > 0; i--) {
          const oldFile = join(this.logDir, `app.log.${i}`)
          const newFile = join(this.logDir, `app.log.${i + 1}`)
          
          try {
            await fs.rename(oldFile, newFile)
          } catch {
            // File doesn't exist, continue
          }
        }
        
        // Move current log to .1
        await fs.rename(this.logFile, join(this.logDir, 'app.log.1'))
      }
    } catch (error) {
      // Log file doesn't exist yet, no need to rotate
    }
  }

  public async getLogs(limit: number = 100): Promise<LogEntry[]> {
    try {
      const content = await fs.readFile(this.logFile, 'utf-8')
      const lines = content.trim().split('\n').slice(-limit)
      
      return lines.map(line => {
        try {
          return JSON.parse(line)
        } catch {
          return {
            timestamp: new Date().toISOString(),
            level: LogLevel.INFO,
            message: line
          }
        }
      })
    } catch (error) {
      return []
    }
  }

  public async clearLogs(): Promise<void> {
    try {
      await fs.writeFile(this.logFile, '')
    } catch (error) {
      console.error('Failed to clear logs:', error)
    }
  }
}