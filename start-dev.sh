#!/bin/bash

# JIMU Rovo Electron Development Startup Script

echo "🚀 Starting JIMU Rovo Electron Development Environment..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version check passed: $(node -v)"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ package.json not found. Please run this script from the project root directory."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
    echo "✅ Dependencies installed successfully"
else
    echo "✅ Dependencies already installed"
fi

# Check if all required dependencies are installed
echo "🔍 Checking dependencies..."
npm list --depth=0 > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "⚠️  Some dependencies are missing. Running npm install..."
    npm install
fi

# Start the development server
echo "🎯 Starting development server..."
echo "📱 Frontend will be available at: http://localhost:5173"
echo "🔧 Backend API will be available at: http://localhost:3001"
echo "⚡ Electron app will launch automatically"
echo ""
echo "Press Ctrl+C to stop the development server"
echo ""

npm run dev