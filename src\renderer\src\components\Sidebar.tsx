import React from 'react'
import { Layout, <PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON> } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  MessageOutlined,
  PictureOutlined,
  AppstoreOutlined,
  SearchOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  B<PERSON>bOutlined,
  BulbFilled
} from '@ant-design/icons'

const { Sider } = Layout

interface SidebarProps {
  collapsed: boolean
  onCollapse: (collapsed: boolean) => void
  currentTheme: 'light' | 'dark'
  onThemeToggle: () => void
}

function Sidebar({ collapsed, onCollapse, currentTheme, onThemeToggle }: SidebarProps) {
  const navigate = useNavigate()
  const location = useLocation()

  const menuItems = [
    {
      key: '/chat',
      icon: <MessageOutlined />,
      label: 'Chat',
      onClick: () => navigate('/chat')
    },
    {
      key: '/image',
      icon: <PictureOutlined />,
      label: 'Image Generation',
      onClick: () => navigate('/image')
    },
    {
      key: '/research',
      icon: <SearchOutlined />,
      label: 'Research Agent',
      onClick: () => navigate('/research')
    },
    {
      key: '/plugins',
      icon: <AppstoreOutlined />,
      label: 'Plugins',
      onClick: () => navigate('/plugins')
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: 'Settings',
      onClick: () => navigate('/settings')
    }
  ]

  const selectedKey = location.pathname.startsWith('/chat') ? '/chat' : location.pathname

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={collapsed}
      width={250}
      style={{
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        zIndex: 1000,
        boxShadow: '2px 0 6px rgba(0,0,0,0.1)'
      }}
    >
      <div style={{ 
        height: '64px', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: collapsed ? 'center' : 'space-between',
        padding: collapsed ? '0' : '0 16px',
        borderBottom: '1px solid #f0f0f0'
      }}>
        {!collapsed && (
          <div style={{ 
            fontSize: '18px', 
            fontWeight: 'bold',
            color: currentTheme === 'dark' ? '#fff' : '#000'
          }}>
            JIMU AI
          </div>
        )}
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={() => onCollapse(!collapsed)}
          style={{
            fontSize: '16px',
            width: 32,
            height: 32,
          }}
        />
      </div>

      <Menu
        mode="inline"
        selectedKeys={[selectedKey]}
        style={{ 
          height: 'calc(100vh - 128px)',
          borderRight: 0,
          overflow: 'auto'
        }}
        items={menuItems}
      />

      <div style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: '16px',
        borderTop: '1px solid #f0f0f0'
      }}>
        <Tooltip title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} theme`}>
          <Button
            type="text"
            icon={currentTheme === 'light' ? <BulbOutlined /> : <BulbFilled />}
            onClick={onThemeToggle}
            style={{
              width: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: collapsed ? 'center' : 'flex-start'
            }}
          >
            {!collapsed && (currentTheme === 'light' ? 'Dark Mode' : 'Light Mode')}
          </Button>
        </Tooltip>
      </div>
    </Sider>
  )
}

export default Sidebar