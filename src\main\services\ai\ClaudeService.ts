import Anthropic from '@anthropic-ai/sdk'
import { AIService, ChatCompletionParams, StreamCompletionParams, ChatCompletionResponse, AIServiceConfig, ChatMessage } from './AIService'

export class ClaudeService extends AIService {
  private client: Anthropic

  constructor(config: AIServiceConfig) {
    super(config, 'Claude')
    this.client = new Anthropic({
      apiKey: config.apiKey,
      baseURL: config.baseURL
    })
  }

  private convertMessages(messages: ChatMessage[]): { system?: string; messages: Anthropic.MessageParam[] } {
    const systemMessage = messages.find(m => m.role === 'system')
    const conversationMessages = messages.filter(m => m.role !== 'system')

    return {
      system: systemMessage?.content,
      messages: conversationMessages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content
      }))
    }
  }

  async chatCompletion(params: ChatCompletionParams): Promise<ChatCompletionResponse> {
    try {
      const { system, messages } = this.convertMessages(params.messages)
      
      const response = await this.client.messages.create({
        model: params.model || this.config.model || 'claude-3-sonnet-20240229',
        max_tokens: params.maxTokens ?? this.config.maxTokens ?? 2000,
        temperature: params.temperature ?? this.config.temperature ?? 0.7,
        system,
        messages,
        stream: false
      })

      const content = response.content[0]
      if (content.type !== 'text') {
        throw new Error('Unexpected response type from Claude')
      }

      return {
        content: content.text,
        model: response.model,
        usage: response.usage ? {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens
        } : undefined
      }
    } catch (error) {
      throw this.handleError(error, 'Chat completion')
    }
  }

  async streamCompletion(params: StreamCompletionParams): Promise<void> {
    try {
      const { system, messages } = this.convertMessages(params.messages)
      
      const stream = await this.client.messages.create({
        model: params.model || this.config.model || 'claude-3-sonnet-20240229',
        max_tokens: params.maxTokens ?? this.config.maxTokens ?? 2000,
        temperature: params.temperature ?? this.config.temperature ?? 0.7,
        system,
        messages,
        stream: true
      })

      let fullResponse = ''

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          const content = chunk.delta.text
          fullResponse += content
          params.onChunk?.(content)
        }
      }

      params.onComplete?.(fullResponse)
    } catch (error) {
      const handledError = this.handleError(error, 'Stream completion')
      params.onError?.(handledError)
      throw handledError
    }
  }

  async getModels(): Promise<string[]> {
    // Claude doesn't have a models endpoint, return known models
    return [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307',
      'claude-2.1',
      'claude-2.0',
      'claude-instant-1.2'
    ]
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test with a simple message
      await this.client.messages.create({
        model: 'claude-3-haiku-20240307',
        max_tokens: 10,
        messages: [{ role: 'user', content: 'Hi' }]
      })
      return true
    } catch (error) {
      console.error('Claude config validation failed:', error)
      return false
    }
  }

  updateConfig(newConfig: Partial<AIServiceConfig>): void {
    super.updateConfig(newConfig)
    this.client = new Anthropic({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseURL
    })
  }
}