import React, { useState, useEffect } from 'react'
import { Card, Input, Button, Progress, Typography, List, Tag, Space, Divider } from 'antd'
import { SearchOutlined, FileTextOutlined, LinkOutlined } from '@ant-design/icons'
import { useWebSocket } from '../services/WebSocketService'

const { TextArea } = Input
const { Title, Text, Paragraph } = Typography

interface ResearchResult {
  id: string
  query: string
  status: 'searching' | 'analyzing' | 'synthesizing' | 'completed' | 'failed'
  progress: number
  summary?: string
  sources?: Array<{
    title: string
    url: string
    snippet: string
  }>
  report?: string
  created_at: string
}

function ResearchPage() {
  const { sendMessage, subscribe, connected } = useWebSocket()
  const [query, setQuery] = useState('')
  const [researching, setResearching] = useState(false)
  const [currentResearch, setCurrentResearch] = useState<ResearchResult | null>(null)
  const [researchHistory, setResearchHistory] = useState<ResearchResult[]>([])

  useEffect(() => {
    const unsubscribers = [
      subscribe('research:started', handleResearchStarted),
      subscribe('research:progress', handleResearchProgress),
      subscribe('research:complete', handleResearchComplete),
      subscribe('research:error', handleResearchError)
    ]

    return () => {
      unsubscribers.forEach(unsub => unsub())
    }
  }, [])

  const handleStartResearch = () => {
    if (!query.trim() || !connected) return

    setResearching(true)
    sendMessage('research:start', {
      query: query.trim(),
      options: {
        maxSources: 10,
        includeImages: false,
        language: 'en'
      }
    })
  }

  const handleResearchStarted = (data: any) => {
    const newResearch: ResearchResult = {
      id: data.researchId,
      query,
      status: 'searching',
      progress: 0,
      created_at: new Date().toISOString()
    }
    setCurrentResearch(newResearch)
  }

  const handleResearchProgress = (data: any) => {
    if (currentResearch && currentResearch.id === data.researchId) {
      setCurrentResearch(prev => prev ? {
        ...prev,
        progress: (data.step / data.totalSteps) * 100,
        status: getStatusFromStep(data.step)
      } : null)
    }
  }

  const handleResearchComplete = (data: any) => {
    if (currentResearch && currentResearch.id === data.researchId) {
      const completedResearch: ResearchResult = {
        ...currentResearch,
        status: 'completed',
        progress: 100,
        summary: data.result.summary,
        sources: data.result.sources,
        report: data.result.report
      }
      
      setCurrentResearch(completedResearch)
      setResearchHistory(prev => [completedResearch, ...prev])
      setResearching(false)
    }
  }

  const handleResearchError = (data: any) => {
    setResearching(false)
    if (currentResearch) {
      setCurrentResearch(prev => prev ? { ...prev, status: 'failed' } : null)
    }
  }

  const getStatusFromStep = (step: number): ResearchResult['status'] => {
    switch (step) {
      case 1: return 'searching'
      case 2: return 'analyzing'
      case 3: return 'synthesizing'
      case 4: return 'completed'
      default: return 'searching'
    }
  }

  const getStatusColor = (status: ResearchResult['status']) => {
    switch (status) {
      case 'searching': return 'blue'
      case 'analyzing': return 'orange'
      case 'synthesizing': return 'purple'
      case 'completed': return 'green'
      case 'failed': return 'red'
      default: return 'default'
    }
  }

  const getStatusText = (status: ResearchResult['status']) => {
    switch (status) {
      case 'searching': return 'Searching...'
      case 'analyzing': return 'Analyzing...'
      case 'synthesizing': return 'Synthesizing...'
      case 'completed': return 'Completed'
      case 'failed': return 'Failed'
      default: return 'Unknown'
    }
  }

  return (
    <div style={{ padding: '24px', height: '100vh', overflow: 'auto' }}>
      <Title level={2}>Research Agent</Title>
      
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
            Research Query
          </label>
          <TextArea
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Enter your research question or topic..."
            rows={3}
            maxLength={500}
            showCount
          />
        </div>

        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={handleStartResearch}
          loading={researching}
          disabled={!query.trim() || !connected}
          size="large"
        >
          {researching ? 'Researching...' : 'Start Research'}
        </Button>

        {!connected && (
          <div style={{ marginTop: '16px', color: '#ff4d4f' }}>
            Disconnected from server
          </div>
        )}
      </Card>

      {currentResearch && (
        <Card 
          title={
            <Space>
              <FileTextOutlined />
              Current Research
              <Tag color={getStatusColor(currentResearch.status)}>
                {getStatusText(currentResearch.status)}
              </Tag>
            </Space>
          }
          style={{ marginBottom: '24px' }}
        >
          <div style={{ marginBottom: '16px' }}>
            <Text strong>Query:</Text> {currentResearch.query}
          </div>
          
          <Progress 
            percent={Math.round(currentResearch.progress)} 
            status={currentResearch.status === 'failed' ? 'exception' : 'active'}
            style={{ marginBottom: '16px' }}
          />

          {currentResearch.summary && (
            <div style={{ marginBottom: '16px' }}>
              <Text strong>Summary:</Text>
              <Paragraph style={{ marginTop: '8px' }}>
                {currentResearch.summary}
              </Paragraph>
            </div>
          )}

          {currentResearch.sources && currentResearch.sources.length > 0 && (
            <div style={{ marginBottom: '16px' }}>
              <Text strong>Sources:</Text>
              <List
                size="small"
                dataSource={currentResearch.sources}
                renderItem={(source, index) => (
                  <List.Item>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Space>
                        <LinkOutlined />
                        <a href={source.url} target="_blank" rel="noopener noreferrer">
                          {source.title}
                        </a>
                      </Space>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {source.snippet}
                      </Text>
                    </Space>
                  </List.Item>
                )}
              />
            </div>
          )}

          {currentResearch.report && (
            <div>
              <Text strong>Research Report:</Text>
              <Card style={{ marginTop: '8px', backgroundColor: '#fafafa' }}>
                <Paragraph style={{ whiteSpace: 'pre-wrap' }}>
                  {currentResearch.report}
                </Paragraph>
              </Card>
            </div>
          )}
        </Card>
      )}

      {researchHistory.length > 0 && (
        <Card title="Research History">
          <List
            dataSource={researchHistory}
            renderItem={(research) => (
              <List.Item>
                <Card 
                  size="small" 
                  style={{ width: '100%' }}
                  title={
                    <Space>
                      <Text ellipsis style={{ maxWidth: '400px' }}>
                        {research.query}
                      </Text>
                      <Tag color={getStatusColor(research.status)}>
                        {getStatusText(research.status)}
                      </Tag>
                    </Space>
                  }
                  extra={
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {new Date(research.created_at).toLocaleString()}
                    </Text>
                  }
                >
                  {research.summary && (
                    <Paragraph 
                      ellipsis={{ rows: 2, expandable: true }}
                      style={{ margin: 0 }}
                    >
                      {research.summary}
                    </Paragraph>
                  )}
                  
                  {research.sources && research.sources.length > 0 && (
                    <div style={{ marginTop: '8px' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {research.sources.length} sources found
                      </Text>
                    </div>
                  )}
                </Card>
              </List.Item>
            )}
          />
        </Card>
      )}

      {researchHistory.length === 0 && !currentResearch && (
        <Card style={{ textAlign: 'center', padding: '50px' }}>
          <SearchOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
          <Title level={4} style={{ color: '#999' }}>No Research History</Title>
          <Paragraph style={{ color: '#999' }}>
            Start your first research by entering a query above.
          </Paragraph>
        </Card>
      )}
    </div>
  )
}

export default ResearchPage