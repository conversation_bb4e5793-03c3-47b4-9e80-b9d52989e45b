import { ConfigService } from '../../src/main/services/ConfigService'
import { DatabaseService } from '../../src/main/database/DatabaseService'

// Mock DatabaseService
jest.mock('../../src/main/database/DatabaseService')

describe('ConfigService', () => {
  let configService: ConfigService
  let mockDb: jest.Mocked<DatabaseService>

  beforeEach(() => {
    mockDb = {
      get: jest.fn(),
      run: jest.fn(),
      all: jest.fn()
    } as any

    DatabaseService.getInstance = jest.fn(() => mockDb)
    configService = ConfigService.getInstance()
  })

  describe('get', () => {
    it('should return cached value if available', async () => {
      // Setup
      const key = 'test.key'
      const value = 'test value'
      configService['cache'].set(key, value)

      // Execute
      const result = await configService.get(key)

      // Verify
      expect(result).toBe(value)
      expect(mockDb.get).not.toHaveBeenCalled()
    })

    it('should fetch from database if not cached', async () => {
      // Setup
      const key = 'test.key'
      const value = 'test value'
      mockDb.get.mockResolvedValue({ value: JSON.stringify(value) })

      // Execute
      const result = await configService.get(key)

      // Verify
      expect(result).toBe(value)
      expect(mockDb.get).toHaveBeenCalledWith(
        'SELECT value FROM config WHERE key = ?',
        [key]
      )
    })

    it('should return undefined for non-existent key', async () => {
      // Setup
      const key = 'nonexistent.key'
      mockDb.get.mockResolvedValue(null)

      // Execute
      const result = await configService.get(key)

      // Verify
      expect(result).toBeUndefined()
    })
  })

  describe('set', () => {
    it('should save value to database and cache', async () => {
      // Setup
      const key = 'test.key'
      const value = 'test value'
      mockDb.run.mockResolvedValue({ changes: 1 })

      // Execute
      await configService.set(key, value)

      // Verify
      expect(mockDb.run).toHaveBeenCalledWith(
        'INSERT OR REPLACE INTO config (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
        [key, JSON.stringify(value)]
      )
      expect(configService['cache'].get(key)).toBe(value)
    })

    it('should emit configChanged event', async () => {
      // Setup
      const key = 'test.key'
      const value = 'test value'
      const eventSpy = jest.fn()
      configService.on('configChanged', eventSpy)
      mockDb.run.mockResolvedValue({ changes: 1 })

      // Execute
      await configService.set(key, value)

      // Verify
      expect(eventSpy).toHaveBeenCalledWith({ key, value })
    })
  })

  describe('watch', () => {
    it('should call callback when config changes', async () => {
      // Setup
      const key = 'test.key'
      const value = 'test value'
      const callback = jest.fn()
      mockDb.run.mockResolvedValue({ changes: 1 })

      // Execute
      const unwatch = configService.watch(key, callback)
      await configService.set(key, value)

      // Verify
      expect(callback).toHaveBeenCalledWith(value)

      // Cleanup
      unwatch()
    })
  })
})