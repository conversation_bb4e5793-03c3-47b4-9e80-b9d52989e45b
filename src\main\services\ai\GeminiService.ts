import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold } from '@google/generative-ai'
import { AIService, ChatCompletionParams, StreamCompletionParams, ChatCompletionResponse, AIServiceConfig, ChatMessage } from './AIService'

export class GeminiService extends AIService {
  private client: GoogleGenerativeAI

  constructor(config: AIServiceConfig) {
    super(config, 'Gemini')
    this.client = new GoogleGenerativeAI(config.apiKey)
  }

  private convertMessages(messages: ChatMessage[]) {
    const systemMessage = messages.find(m => m.role === 'system')
    const conversationMessages = messages.filter(m => m.role !== 'system')

    const history = conversationMessages.slice(0, -1).map(msg => ({
      role: msg.role === 'assistant' ? 'model' as const : 'user' as const,
      parts: [{ text: msg.content }]
    }))

    const lastMessage = conversationMessages[conversationMessages.length - 1]
    const prompt = lastMessage?.content || ''

    return {
      systemInstruction: systemMessage?.content,
      history,
      prompt
    }
  }

  async chatCompletion(params: ChatCompletionParams): Promise<ChatCompletionResponse> {
    try {
      const model = this.client.getGenerativeModel({
        model: params.model || this.config.model || 'gemini-pro',
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
        generationConfig: {
          temperature: params.temperature ?? this.config.temperature ?? 0.7,
          maxOutputTokens: params.maxTokens ?? this.config.maxTokens ?? 2000,
        }
      })

      const { systemInstruction, history, prompt } = this.convertMessages(params.messages)

      let chat
      if (history.length > 0) {
        chat = model.startChat({
          history,
          systemInstruction
        })
        const result = await chat.sendMessage(prompt)
        const response = await result.response
        
        return {
          content: response.text(),
          model: params.model || this.config.model || 'gemini-pro',
          usage: response.usageMetadata ? {
            promptTokens: response.usageMetadata.promptTokenCount || 0,
            completionTokens: response.usageMetadata.candidatesTokenCount || 0,
            totalTokens: response.usageMetadata.totalTokenCount || 0
          } : undefined
        }
      } else {
        const result = await model.generateContent(prompt)
        const response = await result.response
        
        return {
          content: response.text(),
          model: params.model || this.config.model || 'gemini-pro',
          usage: response.usageMetadata ? {
            promptTokens: response.usageMetadata.promptTokenCount || 0,
            completionTokens: response.usageMetadata.candidatesTokenCount || 0,
            totalTokens: response.usageMetadata.totalTokenCount || 0
          } : undefined
        }
      }
    } catch (error) {
      throw this.handleError(error, 'Chat completion')
    }
  }

  async streamCompletion(params: StreamCompletionParams): Promise<void> {
    try {
      const model = this.client.getGenerativeModel({
        model: params.model || this.config.model || 'gemini-pro',
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
          },
        ],
        generationConfig: {
          temperature: params.temperature ?? this.config.temperature ?? 0.7,
          maxOutputTokens: params.maxTokens ?? this.config.maxTokens ?? 2000,
        }
      })

      const { systemInstruction, history, prompt } = this.convertMessages(params.messages)

      let result
      if (history.length > 0) {
        const chat = model.startChat({
          history,
          systemInstruction
        })
        result = await chat.sendMessageStream(prompt)
      } else {
        result = await model.generateContentStream(prompt)
      }

      let fullResponse = ''

      for await (const chunk of result.stream) {
        const chunkText = chunk.text()
        fullResponse += chunkText
        params.onChunk?.(chunkText)
      }

      params.onComplete?.(fullResponse)
    } catch (error) {
      const handledError = this.handleError(error, 'Stream completion')
      params.onError?.(handledError)
      throw handledError
    }
  }

  async getModels(): Promise<string[]> {
    try {
      const models = await this.client.listModels()
      return models.map(model => model.name.replace('models/', ''))
        .filter(name => name.includes('gemini'))
        .sort()
    } catch (error) {
      // Fallback to known models if API call fails
      return [
        'gemini-pro',
        'gemini-pro-vision',
        'gemini-1.5-pro',
        'gemini-1.5-flash'
      ]
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      const model = this.client.getGenerativeModel({ model: 'gemini-pro' })
      await model.generateContent('Hi')
      return true
    } catch (error) {
      console.error('Gemini config validation failed:', error)
      return false
    }
  }

  updateConfig(newConfig: Partial<AIServiceConfig>): void {
    super.updateConfig(newConfig)
    this.client = new GoogleGenerativeAI(this.config.apiKey)
  }
}