import { Application } from 'express'
import { ConfigService } from '../services/ConfigService'
import { DatabaseService } from '../database/DatabaseService'

export function setupRoutes(app: Application): void {
  const config = ConfigService.getInstance()
  const db = DatabaseService.getInstance()

  // Health check
  app.get('/api/health', (req, res) => {
    res.json({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    })
  })

  // Configuration routes
  app.get('/api/config', async (req, res) => {
    try {
      const allConfig = await config.getAll()
      res.json(allConfig)
    } catch (error) {
      res.status(500).json({ error: 'Failed to get configuration' })
    }
  })

  app.get('/api/config/:key', async (req, res) => {
    try {
      const value = await config.get(req.params.key)
      if (value !== undefined) {
        res.json({ key: req.params.key, value })
      } else {
        res.status(404).json({ error: 'Configuration key not found' })
      }
    } catch (error) {
      res.status(500).json({ error: 'Failed to get configuration' })
    }
  })

  app.post('/api/config/:key', async (req, res) => {
    try {
      await config.set(req.params.key, req.body.value)
      res.json({ success: true })
    } catch (error) {
      res.status(500).json({ error: 'Failed to set configuration' })
    }
  })

  app.delete('/api/config/:key', async (req, res) => {
    try {
      await config.delete(req.params.key)
      res.json({ success: true })
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete configuration' })
    }
  })

  // Authentication routes
  app.post('/api/auth/register', async (req, res) => {
    try {
      const { username, email, password } = req.body
      
      // TODO: Implement password hashing and validation
      const result = await db.run(
        'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
        [username, email, password] // TODO: Hash password
      )
      
      res.json({ success: true, userId: result.id })
    } catch (error) {
      res.status(500).json({ error: 'Registration failed' })
    }
  })

  app.post('/api/auth/login', async (req, res) => {
    try {
      const { username, password } = req.body
      
      const user = await db.get(
        'SELECT * FROM users WHERE username = ?',
        [username]
      )
      
      if (user) {
        // TODO: Implement password verification and JWT token generation
        res.json({ success: true, user: { id: user.id, username: user.username } })
      } else {
        res.status(401).json({ error: 'Invalid credentials' })
      }
    } catch (error) {
      res.status(500).json({ error: 'Login failed' })
    }
  })

  // Conversation routes
  app.get('/api/conversations', async (req, res) => {
    try {
      const conversations = await db.all(
        'SELECT * FROM conversations ORDER BY updated_at DESC'
      )
      res.json(conversations)
    } catch (error) {
      res.status(500).json({ error: 'Failed to get conversations' })
    }
  })

  app.post('/api/conversations', async (req, res) => {
    try {
      const { title, userId } = req.body
      const id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      await db.run(
        'INSERT INTO conversations (id, user_id, title) VALUES (?, ?, ?)',
        [id, userId || 1, title || 'New Conversation']
      )
      
      res.json({ success: true, conversationId: id })
    } catch (error) {
      res.status(500).json({ error: 'Failed to create conversation' })
    }
  })

  app.get('/api/conversations/:id/messages', async (req, res) => {
    try {
      const messages = await db.all(
        'SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at ASC',
        [req.params.id]
      )
      res.json(messages)
    } catch (error) {
      res.status(500).json({ error: 'Failed to get messages' })
    }
  })

  // Plugin routes
  app.get('/api/plugins', async (req, res) => {
    try {
      const plugins = await db.all('SELECT * FROM plugins')
      res.json(plugins)
    } catch (error) {
      res.status(500).json({ error: 'Failed to get plugins' })
    }
  })

  app.post('/api/plugins/:id/toggle', async (req, res) => {
    try {
      const { enabled } = req.body
      await db.run(
        'UPDATE plugins SET enabled = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [enabled ? 1 : 0, req.params.id]
      )
      res.json({ success: true })
    } catch (error) {
      res.status(500).json({ error: 'Failed to toggle plugin' })
    }
  })

  // Image generation routes
  app.get('/api/images', async (req, res) => {
    try {
      const images = await db.all(
        'SELECT * FROM image_generations ORDER BY created_at DESC LIMIT 50'
      )
      res.json(images)
    } catch (error) {
      res.status(500).json({ error: 'Failed to get images' })
    }
  })

  app.post('/api/images/generate', async (req, res) => {
    try {
      const { prompt, model, userId } = req.body
      const id = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      // TODO: Implement actual image generation
      await db.run(
        'INSERT INTO image_generations (id, user_id, prompt, model) VALUES (?, ?, ?, ?)',
        [id, userId || 1, prompt, model || 'dall-e-3']
      )
      
      res.json({ success: true, imageId: id, status: 'pending' })
    } catch (error) {
      res.status(500).json({ error: 'Failed to start image generation' })
    }
  })
}