import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface ConfigContextType {
  config: Record<string, any>
  getConfig: (key: string) => any
  setConfig: (key: string, value: any) => Promise<void>
  loading: boolean
}

const ConfigContext = createContext<ConfigContextType | null>(null)

interface ConfigProviderProps {
  children: ReactNode
}

export function ConfigProvider({ children }: ConfigProviderProps) {
  const [config, setConfigState] = useState<Record<string, any>>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    try {
      setLoading(true)
      const response = await fetch('http://localhost:3001/api/config')
      if (response.ok) {
        const configData = await response.json()
        setConfigState(configData)
      }
    } catch (error) {
      console.error('Failed to load config:', error)
    } finally {
      setLoading(false)
    }
  }

  const getConfig = (key: string) => {
    return config[key]
  }

  const setConfig = async (key: string, value: any) => {
    try {
      const response = await fetch(`http://localhost:3001/api/config/${key}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ value })
      })

      if (response.ok) {
        setConfigState(prev => ({
          ...prev,
          [key]: value
        }))
      }
    } catch (error) {
      console.error('Failed to set config:', error)
      throw error
    }
  }

  const value: ConfigContextType = {
    config,
    getConfig,
    setConfig,
    loading
  }

  return (
    <ConfigContext.Provider value={value}>
      {children}
    </ConfigContext.Provider>
  )
}

export function useConfig() {
  const context = useContext(ConfigContext)
  if (!context) {
    throw new Error('useConfig must be used within a ConfigProvider')
  }
  return context
}