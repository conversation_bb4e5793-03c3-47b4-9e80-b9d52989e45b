{"name": "jimu-nodejs-electron", "version": "1.0.0", "description": "JIMU AI Chat Desktop - Pure Node.js Edition", "main": "out/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["electron", "ai", "chat", "desktop"], "author": "JIMU Team", "license": "MIT", "dependencies": {"@ant-design/icons": "^6.0.0", "@anthropic-ai/sdk": "^0.17.0", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@google/generative-ai": "^0.2.0", "@types/node": "^20.0.0", "@vitejs/plugin-react": "^4.0.0", "antd": "^5.26.3", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "electron-builder": "^24.0.0", "electron-vite": "^3.1.0", "express": "^4.18.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "openai": "^4.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "socket.io": "^4.7.0", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.0", "typescript": "^5.0.0", "vite": "^5.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "electron": "^37.2.0"}}