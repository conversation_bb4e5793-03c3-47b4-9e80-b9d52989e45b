{"name": "jimu-nodejs-electron", "version": "1.0.0", "description": "JIMU AI Chat Desktop - Pure Node.js Edition", "main": "dist/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["electron", "ai", "chat", "desktop"], "author": "JIMU Team", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.17.0", "@google/generative-ai": "^0.2.0", "@types/node": "^20.0.0", "@vitejs/plugin-react": "^4.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "electron-builder": "^24.0.0", "electron-vite": "^3.1.0", "express": "^4.18.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "openai": "^4.0.0", "socket.io": "^4.7.0", "sqlite3": "^5.1.0", "typescript": "^5.0.0", "vite": "^5.0.0", "winston": "^3.10.0"}, "devDependencies": {"electron": "^37.2.0"}}