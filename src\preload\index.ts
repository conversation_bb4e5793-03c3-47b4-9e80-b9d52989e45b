import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// Custom APIs for renderer
const api = {
  // Config management
  config: {
    get: (key: string) => ipcRenderer.invoke('config:get', key),
    set: (key: string, value: any) => ipcRenderer.invoke('config:set', key, value),
    watch: (key: string, callback: Function) => {
      const handler = (_: any, data: any) => callback(data)
      ipcRenderer.on(`config:changed:${key}`, handler)
      return () => ipcRenderer.removeListener(`config:changed:${key}`, handler)
    }
  },
  
  // Database operations
  db: {
    query: (sql: string, params?: any[]) => ipcRenderer.invoke('db:query', sql, params),
    run: (sql: string, params?: any[]) => ipcRenderer.invoke('db:run', sql, params),
    get: (sql: string, params?: any[]) => ipcRenderer.invoke('db:get', sql, params),
    all: (sql: string, params?: any[]) => ipcRenderer.invoke('db:all', sql, params)
  },

  // File operations
  file: {
    save: (data: any, filename: string) => ipcRenderer.invoke('file:save', data, filename),
    load: (filename: string) => ipcRenderer.invoke('file:load', filename),
    delete: (filename: string) => ipcRenderer.invoke('file:delete', filename),
    exists: (filename: string) => ipcRenderer.invoke('file:exists', filename)
  },

  // System operations
  system: {
    getVersion: () => ipcRenderer.invoke('system:version'),
    getPlatform: () => ipcRenderer.invoke('system:platform'),
    openExternal: (url: string) => ipcRenderer.invoke('system:openExternal', url)
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}