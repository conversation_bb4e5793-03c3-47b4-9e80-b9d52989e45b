export interface ImageGenerationParams {
  prompt: string
  model?: string
  size?: string
  quality?: 'standard' | 'hd'
  style?: 'vivid' | 'natural'
  n?: number
}

export interface ImageResult {
  id: string
  url: string
  revisedPrompt?: string
  size: string
  model: string
  created: number
}

export interface ImageServiceConfig {
  apiKey: string
  baseURL?: string
  model?: string
  defaultSize?: string
  defaultQuality?: 'standard' | 'hd'
}

export abstract class ImageService {
  protected config: ImageServiceConfig
  protected serviceName: string

  constructor(config: ImageServiceConfig, serviceName: string) {
    this.config = config
    this.serviceName = serviceName
  }

  abstract generateImage(params: ImageGenerationParams): Promise<ImageResult>
  abstract getSupportedModels(): string[]
  abstract getSupportedSizes(): string[]
  abstract validateConfig(): Promise<boolean>

  protected handleError(error: any, context: string): Error {
    console.error(`[${this.serviceName}] ${context}:`, error)
    
    if (error.response?.status === 401) {
      return new Error(`Invalid API key for ${this.serviceName}`)
    } else if (error.response?.status === 429) {
      return new Error(`Rate limit exceeded for ${this.serviceName}`)
    } else if (error.response?.status === 400) {
      return new Error(`Invalid request to ${this.serviceName}: ${error.response?.data?.error?.message || error.message}`)
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return new Error(`Network error connecting to ${this.serviceName}`)
    }
    
    return new Error(`${this.serviceName} error: ${error.message || 'Unknown error'}`)
  }

  public getServiceName(): string {
    return this.serviceName
  }

  public updateConfig(newConfig: Partial<ImageServiceConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}