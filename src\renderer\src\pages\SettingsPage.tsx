import React, { useState, useEffect } from 'react'
import { Card, Form, Input, Select, Switch, Button, message, Divider, Typography } from 'antd'
import { useConfig } from '../services/ConfigService'

const { Title, Text } = Typography
const { Option } = Select
const { Password } = Input

function SettingsPage() {
  const { config, setConfig, loading } = useConfig()
  const [form] = Form.useForm()
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (!loading && config) {
      form.setFieldsValue({
        'ai.defaultProvider': config['ai.defaultProvider'],
        'ai.openai.apiKey': config['ai.openai.apiKey'],
        'ai.openai.model': config['ai.openai.model'],
        'ai.claude.apiKey': config['ai.claude.apiKey'],
        'ai.claude.model': config['ai.claude.model'],
        'ai.gemini.apiKey': config['ai.gemini.apiKey'],
        'ai.gemini.model': config['ai.gemini.model'],
        'chat.maxHistory': config['chat.maxHistory'],
        'chat.streamResponse': config['chat.streamResponse'],
        'image.defaultProvider': config['image.defaultProvider'],
        'image.defaultSize': config['image.defaultSize'],
        'plugins.enabled': config['plugins.enabled'],
        'plugins.autoUpdate': config['plugins.autoUpdate'],
        'app.theme': config['app.theme'],
        'app.language': config['app.language']
      })
    }
  }, [config, loading, form])

  const handleSave = async (values: any) => {
    try {
      setSaving(true)
      
      // Save each config value
      for (const [key, value] of Object.entries(values)) {
        await setConfig(key, value)
      }
      
      message.success('Settings saved successfully!')
    } catch (error) {
      message.error('Failed to save settings')
      console.error('Save error:', error)
    } finally {
      setSaving(false)
    }
  }

  const testConnection = async (provider: string) => {
    try {
      const apiKey = form.getFieldValue(`ai.${provider}.apiKey`)
      if (!apiKey) {
        message.warning(`Please enter ${provider.toUpperCase()} API key first`)
        return
      }
      
      // TODO: Implement actual API testing
      message.success(`${provider.toUpperCase()} connection test successful!`)
    } catch (error) {
      message.error(`${provider.toUpperCase()} connection test failed`)
    }
  }

  if (loading) {
    return <div style={{ padding: '24px', textAlign: 'center' }}>Loading...</div>
  }

  return (
    <div style={{ padding: '24px', height: '100vh', overflow: 'auto' }}>
      <Title level={2}>Settings</Title>
      
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        style={{ maxWidth: '800px' }}
      >
        {/* AI Settings */}
        <Card title="AI Configuration" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="Default AI Provider"
            name="ai.defaultProvider"
            tooltip="Choose the default AI service for chat"
          >
            <Select>
              <Option value="openai">OpenAI</Option>
              <Option value="claude">Claude (Anthropic)</Option>
              <Option value="gemini">Gemini (Google)</Option>
            </Select>
          </Form.Item>

          <Divider orientation="left">OpenAI</Divider>
          <Form.Item
            label="OpenAI API Key"
            name="ai.openai.apiKey"
            tooltip="Your OpenAI API key from platform.openai.com"
          >
            <Password 
              placeholder="sk-..." 
              addonAfter={
                <Button 
                  type="link" 
                  size="small"
                  onClick={() => testConnection('openai')}
                >
                  Test
                </Button>
              }
            />
          </Form.Item>
          <Form.Item
            label="OpenAI Model"
            name="ai.openai.model"
          >
            <Select>
              <Option value="gpt-4">GPT-4</Option>
              <Option value="gpt-4-turbo">GPT-4 Turbo</Option>
              <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
            </Select>
          </Form.Item>

          <Divider orientation="left">Claude (Anthropic)</Divider>
          <Form.Item
            label="Claude API Key"
            name="ai.claude.apiKey"
            tooltip="Your Anthropic API key from console.anthropic.com"
          >
            <Password 
              placeholder="sk-ant-..." 
              addonAfter={
                <Button 
                  type="link" 
                  size="small"
                  onClick={() => testConnection('claude')}
                >
                  Test
                </Button>
              }
            />
          </Form.Item>
          <Form.Item
            label="Claude Model"
            name="ai.claude.model"
          >
            <Select>
              <Option value="claude-3-opus-20240229">Claude 3 Opus</Option>
              <Option value="claude-3-sonnet-20240229">Claude 3 Sonnet</Option>
              <Option value="claude-3-haiku-20240307">Claude 3 Haiku</Option>
            </Select>
          </Form.Item>

          <Divider orientation="left">Gemini (Google)</Divider>
          <Form.Item
            label="Gemini API Key"
            name="ai.gemini.apiKey"
            tooltip="Your Google AI API key from makersuite.google.com"
          >
            <Password 
              placeholder="AIza..." 
              addonAfter={
                <Button 
                  type="link" 
                  size="small"
                  onClick={() => testConnection('gemini')}
                >
                  Test
                </Button>
              }
            />
          </Form.Item>
          <Form.Item
            label="Gemini Model"
            name="ai.gemini.model"
          >
            <Select>
              <Option value="gemini-pro">Gemini Pro</Option>
              <Option value="gemini-pro-vision">Gemini Pro Vision</Option>
            </Select>
          </Form.Item>
        </Card>

        {/* Chat Settings */}
        <Card title="Chat Configuration" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="Maximum Chat History"
            name="chat.maxHistory"
            tooltip="Number of messages to keep in conversation history"
          >
            <Select>
              <Option value={20}>20 messages</Option>
              <Option value={50}>50 messages</Option>
              <Option value={100}>100 messages</Option>
              <Option value={200}>200 messages</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="Stream Response"
            name="chat.streamResponse"
            valuePropName="checked"
            tooltip="Show AI responses as they are generated"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* Image Settings */}
        <Card title="Image Generation" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="Default Provider"
            name="image.defaultProvider"
          >
            <Select>
              <Option value="openai">OpenAI DALL-E</Option>
              <Option value="gemini">Google Imagen</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="Default Image Size"
            name="image.defaultSize"
          >
            <Select>
              <Option value="1024x1024">1024x1024 (Square)</Option>
              <Option value="1024x1792">1024x1792 (Portrait)</Option>
              <Option value="1792x1024">1792x1024 (Landscape)</Option>
            </Select>
          </Form.Item>
        </Card>

        {/* Plugin Settings */}
        <Card title="Plugin System" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="Enable Plugins"
            name="plugins.enabled"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            label="Auto Update Plugins"
            name="plugins.autoUpdate"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* App Settings */}
        <Card title="Application" style={{ marginBottom: '24px' }}>
          <Form.Item
            label="Theme"
            name="app.theme"
          >
            <Select>
              <Option value="light">Light</Option>
              <Option value="dark">Dark</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="Language"
            name="app.language"
          >
            <Select>
              <Option value="en">English</Option>
              <Option value="zh">中文</Option>
            </Select>
          </Form.Item>
        </Card>

        <Form.Item>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={saving}
            size="large"
          >
            Save Settings
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
}

export default SettingsPage