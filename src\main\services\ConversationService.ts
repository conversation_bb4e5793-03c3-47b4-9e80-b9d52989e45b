import { DatabaseService } from '../database/DatabaseService'

export interface Message {
  id: string
  conversation_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  metadata?: any
  created_at: string
}

export interface Conversation {
  id: string
  user_id: number
  title: string
  created_at: string
  updated_at: string
}

export class ConversationService {
  private static instance: ConversationService
  private db: DatabaseService

  private constructor() {
    this.db = DatabaseService.getInstance()
  }

  public static getInstance(): ConversationService {
    if (!ConversationService.instance) {
      ConversationService.instance = new ConversationService()
    }
    return ConversationService.instance
  }

  public async createConversation(userId: number, title?: string): Promise<string> {
    try {
      const conversationId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const conversationTitle = title || this.generateDefaultTitle()

      await this.db.run(
        'INSERT INTO conversations (id, user_id, title) VALUES (?, ?, ?)',
        [conversationId, userId, conversationTitle]
      )

      return conversationId
    } catch (error) {
      console.error('Failed to create conversation:', error)
      throw new Error('Failed to create conversation')
    }
  }

  public async saveMessage(conversationId: string, role: 'user' | 'assistant' | 'system', content: string, metadata?: any): Promise<string> {
    try {
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      const metadataJson = metadata ? JSON.stringify(metadata) : null

      await this.db.run(
        'INSERT INTO messages (id, conversation_id, role, content, metadata) VALUES (?, ?, ?, ?, ?)',
        [messageId, conversationId, role, content, metadataJson]
      )

      // Update conversation timestamp
      await this.db.run(
        'UPDATE conversations SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [conversationId]
      )

      return messageId
    } catch (error) {
      console.error('Failed to save message:', error)
      throw new Error('Failed to save message')
    }
  }

  public async getConversation(conversationId: string): Promise<Conversation | null> {
    try {
      const conversation = await this.db.get(
        'SELECT * FROM conversations WHERE id = ?',
        [conversationId]
      )
      return conversation || null
    } catch (error) {
      console.error('Failed to get conversation:', error)
      return null
    }
  }

  public async getConversations(userId: number, limit: number = 50, offset: number = 0): Promise<Conversation[]> {
    try {
      const conversations = await this.db.all(
        'SELECT * FROM conversations WHERE user_id = ? ORDER BY updated_at DESC LIMIT ? OFFSET ?',
        [userId, limit, offset]
      )
      return conversations
    } catch (error) {
      console.error('Failed to get conversations:', error)
      return []
    }
  }

  public async getMessages(conversationId: string, limit: number = 100, offset: number = 0): Promise<Message[]> {
    try {
      const messages = await this.db.all(
        'SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at ASC LIMIT ? OFFSET ?',
        [conversationId, limit, offset]
      )

      return messages.map(msg => ({
        ...msg,
        metadata: msg.metadata ? JSON.parse(msg.metadata) : null
      }))
    } catch (error) {
      console.error('Failed to get messages:', error)
      return []
    }
  }

  public async updateConversationTitle(conversationId: string, title: string): Promise<boolean> {
    try {
      const result = await this.db.run(
        'UPDATE conversations SET title = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [title, conversationId]
      )
      return result.changes > 0
    } catch (error) {
      console.error('Failed to update conversation title:', error)
      return false
    }
  }

  public async deleteConversation(conversationId: string): Promise<boolean> {
    try {
      // Delete messages first
      await this.db.run('DELETE FROM messages WHERE conversation_id = ?', [conversationId])
      
      // Delete conversation
      const result = await this.db.run('DELETE FROM conversations WHERE id = ?', [conversationId])
      
      return result.changes > 0
    } catch (error) {
      console.error('Failed to delete conversation:', error)
      return false
    }
  }

  public async deleteMessage(messageId: string): Promise<boolean> {
    try {
      const result = await this.db.run('DELETE FROM messages WHERE id = ?', [messageId])
      return result.changes > 0
    } catch (error) {
      console.error('Failed to delete message:', error)
      return false
    }
  }

  public async getConversationStats(conversationId: string): Promise<{
    messageCount: number
    firstMessageDate: string | null
    lastMessageDate: string | null
  }> {
    try {
      const stats = await this.db.get(`
        SELECT 
          COUNT(*) as messageCount,
          MIN(created_at) as firstMessageDate,
          MAX(created_at) as lastMessageDate
        FROM messages 
        WHERE conversation_id = ?
      `, [conversationId])

      return {
        messageCount: stats?.messageCount || 0,
        firstMessageDate: stats?.firstMessageDate || null,
        lastMessageDate: stats?.lastMessageDate || null
      }
    } catch (error) {
      console.error('Failed to get conversation stats:', error)
      return {
        messageCount: 0,
        firstMessageDate: null,
        lastMessageDate: null
      }
    }
  }

  public async searchMessages(query: string, userId: number, limit: number = 20): Promise<Message[]> {
    try {
      const messages = await this.db.all(`
        SELECT m.* FROM messages m
        JOIN conversations c ON m.conversation_id = c.id
        WHERE c.user_id = ? AND m.content LIKE ?
        ORDER BY m.created_at DESC
        LIMIT ?
      `, [userId, `%${query}%`, limit])

      return messages.map(msg => ({
        ...msg,
        metadata: msg.metadata ? JSON.parse(msg.metadata) : null
      }))
    } catch (error) {
      console.error('Failed to search messages:', error)
      return []
    }
  }

  public async generateConversationTitle(conversationId: string): Promise<string> {
    try {
      const messages = await this.getMessages(conversationId, 5)
      const userMessages = messages.filter(m => m.role === 'user')
      
      if (userMessages.length === 0) {
        return this.generateDefaultTitle()
      }

      // Use first user message to generate title
      const firstMessage = userMessages[0].content
      const words = firstMessage.split(' ').slice(0, 6)
      let title = words.join(' ')
      
      if (firstMessage.length > title.length) {
        title += '...'
      }

      return title || this.generateDefaultTitle()
    } catch (error) {
      console.error('Failed to generate conversation title:', error)
      return this.generateDefaultTitle()
    }
  }

  private generateDefaultTitle(): string {
    const now = new Date()
    return `Conversation ${now.toLocaleDateString()} ${now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`
  }

  public async cleanupOldConversations(userId: number, keepCount: number = 100): Promise<number> {
    try {
      // Get conversations to delete (older than keepCount)
      const conversationsToDelete = await this.db.all(`
        SELECT id FROM conversations 
        WHERE user_id = ? 
        ORDER BY updated_at DESC 
        LIMIT -1 OFFSET ?
      `, [userId, keepCount])

      let deletedCount = 0
      for (const conv of conversationsToDelete) {
        const success = await this.deleteConversation(conv.id)
        if (success) deletedCount++
      }

      return deletedCount
    } catch (error) {
      console.error('Failed to cleanup old conversations:', error)
      return 0
    }
  }
}