export interface SearchResult {
  title: string
  url: string
  snippet: string
  source: string
  timestamp?: string
}

export interface SearchOptions {
  maxResults?: number
  language?: string
  region?: string
  timeRange?: 'day' | 'week' | 'month' | 'year' | 'all'
}

export abstract class SearchService {
  protected serviceName: string

  constructor(serviceName: string) {
    this.serviceName = serviceName
  }

  abstract search(query: string, options?: SearchOptions): Promise<SearchResult[]>
  abstract isConfigured(): boolean

  protected handleError(error: any, context: string): Error {
    console.error(`[${this.serviceName}] ${context}:`, error)
    return new Error(`${this.serviceName} search error: ${error.message || 'Unknown error'}`)
  }
}

export class TavilySearchService extends SearchService {
  private apiKey: string

  constructor(apiKey: string) {
    super('Tavily')
    this.apiKey = apiKey
  }

  async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    try {
      const response = await fetch('https://api.tavily.com/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          query,
          max_results: options.maxResults || 10,
          search_depth: 'advanced',
          include_answer: false,
          include_images: false,
          include_raw_content: false
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      return data.results?.map((result: any) => ({
        title: result.title || '',
        url: result.url || '',
        snippet: result.content || '',
        source: 'Tavily',
        timestamp: new Date().toISOString()
      })) || []
    } catch (error) {
      throw this.handleError(error, 'Search')
    }
  }

  isConfigured(): boolean {
    return !!this.apiKey
  }
}

export class GoogleSearchService extends SearchService {
  private apiKey: string
  private searchEngineId: string

  constructor(apiKey: string, searchEngineId: string) {
    super('Google')
    this.apiKey = apiKey
    this.searchEngineId = searchEngineId
  }

  async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    try {
      const params = new URLSearchParams({
        key: this.apiKey,
        cx: this.searchEngineId,
        q: query,
        num: Math.min(options.maxResults || 10, 10).toString()
      })

      if (options.language) {
        params.append('lr', `lang_${options.language}`)
      }

      if (options.region) {
        params.append('gl', options.region)
      }

      const response = await fetch(`https://www.googleapis.com/customsearch/v1?${params}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      return data.items?.map((item: any) => ({
        title: item.title || '',
        url: item.link || '',
        snippet: item.snippet || '',
        source: 'Google',
        timestamp: new Date().toISOString()
      })) || []
    } catch (error) {
      throw this.handleError(error, 'Search')
    }
  }

  isConfigured(): boolean {
    return !!(this.apiKey && this.searchEngineId)
  }
}

export class BingSearchService extends SearchService {
  private apiKey: string

  constructor(apiKey: string) {
    super('Bing')
    this.apiKey = apiKey
  }

  async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    try {
      const params = new URLSearchParams({
        q: query,
        count: Math.min(options.maxResults || 10, 50).toString(),
        responseFilter: 'Webpages'
      })

      if (options.language) {
        params.append('setLang', options.language)
      }

      if (options.region) {
        params.append('cc', options.region)
      }

      const response = await fetch(`https://api.bing.microsoft.com/v7.0/search?${params}`, {
        headers: {
          'Ocp-Apim-Subscription-Key': this.apiKey
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      
      return data.webPages?.value?.map((item: any) => ({
        title: item.name || '',
        url: item.url || '',
        snippet: item.snippet || '',
        source: 'Bing',
        timestamp: new Date().toISOString()
      })) || []
    } catch (error) {
      throw this.handleError(error, 'Search')
    }
  }

  isConfigured(): boolean {
    return !!this.apiKey
  }
}

export class SearchManager {
  private services: SearchService[] = []

  constructor() {
    // Services will be initialized based on configuration
  }

  public addService(service: SearchService): void {
    this.services.push(service)
  }

  public async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    const allResults: SearchResult[] = []
    const errors: string[] = []

    for (const service of this.services) {
      if (!service.isConfigured()) {
        continue
      }

      try {
        const results = await service.search(query, options)
        allResults.push(...results)
      } catch (error) {
        errors.push(`${service.constructor.name}: ${error.message}`)
        console.warn(`Search service failed:`, error)
      }
    }

    if (allResults.length === 0 && errors.length > 0) {
      throw new Error(`All search services failed: ${errors.join(', ')}`)
    }

    // Remove duplicates and limit results
    return this.deduplicateResults(allResults).slice(0, options.maxResults || 20)
  }

  private deduplicateResults(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>()
    const deduplicated: SearchResult[] = []

    for (const result of results) {
      const key = this.normalizeUrl(result.url)
      if (!seen.has(key)) {
        seen.add(key)
        deduplicated.push(result)
      }
    }

    return deduplicated
  }

  private normalizeUrl(url: string): string {
    try {
      const parsed = new URL(url)
      return `${parsed.hostname}${parsed.pathname}`
    } catch {
      return url
    }
  }

  public getConfiguredServices(): string[] {
    return this.services
      .filter(service => service.isConfigured())
      .map(service => service.constructor.name)
  }
}