import { AIService, ChatCompletionParams, StreamCompletionParams, ChatCompletionResponse, AIServiceConfig } from './AIService'
import { OpenAIService } from './OpenAIService'
import { ClaudeService } from './ClaudeService'
import { GeminiService } from './GeminiService'
import { ConfigService } from '../ConfigService'

export type AIProvider = 'openai' | 'claude' | 'gemini'

export interface ChatParams extends Omit<ChatCompletionParams, 'model'> {
  provider?: AIProvider
  model?: string
}

export interface StreamParams extends Omit<StreamCompletionParams, 'model'> {
  provider?: AIProvider
  model?: string
}

export class AIManager {
  private static instance: AIManager
  private services: Map<AIProvider, AIService> = new Map()
  private config: ConfigService
  private defaultProvider: AIProvider = 'openai'

  private constructor() {
    this.config = ConfigService.getInstance()
    this.initializeServices()
  }

  public static getInstance(): AIManager {
    if (!AIManager.instance) {
      AIManager.instance = new AIManager()
    }
    return AIManager.instance
  }

  private async initializeServices(): Promise<void> {
    try {
      // Load default provider
      const savedProvider = await this.config.get<AIProvider>('ai.defaultProvider')
      if (savedProvider) {
        this.defaultProvider = savedProvider
      }

      // Initialize OpenAI service
      const openaiConfig = await this.getServiceConfig('openai')
      if (openaiConfig.apiKey) {
        this.services.set('openai', new OpenAIService(openaiConfig))
      }

      // Initialize Claude service
      const claudeConfig = await this.getServiceConfig('claude')
      if (claudeConfig.apiKey) {
        this.services.set('claude', new ClaudeService(claudeConfig))
      }

      // Initialize Gemini service
      const geminiConfig = await this.getServiceConfig('gemini')
      if (geminiConfig.apiKey) {
        this.services.set('gemini', new GeminiService(geminiConfig))
      }

      console.log(`✅ AI Manager initialized with ${this.services.size} services`)
    } catch (error) {
      console.error('Failed to initialize AI services:', error)
    }
  }

  private async getServiceConfig(provider: AIProvider): Promise<AIServiceConfig> {
    const apiKey = await this.config.get<string>(`ai.${provider}.apiKey`) || ''
    const model = await this.config.get<string>(`ai.${provider}.model`)
    const temperature = await this.config.get<number>(`ai.${provider}.temperature`)
    const maxTokens = await this.config.get<number>(`ai.${provider}.maxTokens`)

    return {
      apiKey,
      model,
      temperature,
      maxTokens
    }
  }

  public async chatCompletion(params: ChatParams): Promise<ChatCompletionResponse> {
    const provider = params.provider || this.defaultProvider
    const service = this.services.get(provider)

    if (!service) {
      throw new Error(`AI service '${provider}' is not available. Please check your configuration.`)
    }

    return await service.chatCompletion({
      ...params,
      model: params.model
    })
  }

  public async streamCompletion(params: StreamParams): Promise<void> {
    const provider = params.provider || this.defaultProvider
    const service = this.services.get(provider)

    if (!service) {
      throw new Error(`AI service '${provider}' is not available. Please check your configuration.`)
    }

    return await service.streamCompletion({
      ...params,
      model: params.model
    })
  }

  public async getAvailableModels(provider?: AIProvider): Promise<{ provider: AIProvider; models: string[] }[]> {
    const results: { provider: AIProvider; models: string[] }[] = []

    if (provider) {
      const service = this.services.get(provider)
      if (service) {
        try {
          const models = await service.getModels()
          results.push({ provider, models })
        } catch (error) {
          console.error(`Failed to get models for ${provider}:`, error)
        }
      }
    } else {
      for (const [providerName, service] of this.services.entries()) {
        try {
          const models = await service.getModels()
          results.push({ provider: providerName, models })
        } catch (error) {
          console.error(`Failed to get models for ${providerName}:`, error)
        }
      }
    }

    return results
  }

  public getAvailableProviders(): AIProvider[] {
    return Array.from(this.services.keys())
  }

  public async setDefaultProvider(provider: AIProvider): Promise<void> {
    if (!this.services.has(provider)) {
      throw new Error(`Provider '${provider}' is not available`)
    }

    this.defaultProvider = provider
    await this.config.set('ai.defaultProvider', provider)
  }

  public getDefaultProvider(): AIProvider {
    return this.defaultProvider
  }

  public async updateServiceConfig(provider: AIProvider, config: Partial<AIServiceConfig>): Promise<void> {
    // Update config in database
    for (const [key, value] of Object.entries(config)) {
      if (value !== undefined) {
        await this.config.set(`ai.${provider}.${key}`, value)
      }
    }

    // Update or create service instance
    const fullConfig = await this.getServiceConfig(provider)
    
    if (fullConfig.apiKey) {
      let service: AIService
      
      switch (provider) {
        case 'openai':
          service = new OpenAIService(fullConfig)
          break
        case 'claude':
          service = new ClaudeService(fullConfig)
          break
        case 'gemini':
          service = new GeminiService(fullConfig)
          break
        default:
          throw new Error(`Unknown provider: ${provider}`)
      }

      this.services.set(provider, service)
    } else {
      this.services.delete(provider)
    }
  }

  public async validateService(provider: AIProvider): Promise<boolean> {
    const service = this.services.get(provider)
    if (!service) return false

    try {
      return await service.validateConfig()
    } catch (error) {
      console.error(`Validation failed for ${provider}:`, error)
      return false
    }
  }

  public async validateAllServices(): Promise<{ [key in AIProvider]?: boolean }> {
    const results: { [key in AIProvider]?: boolean } = {}

    for (const provider of this.services.keys()) {
      results[provider] = await this.validateService(provider)
    }

    return results
  }
}