import { DatabaseService } from '../database/DatabaseService'
import { EventEmitter } from 'events'

export interface ConfigValue {
  [key: string]: any
}

export class ConfigService extends EventEmitter {
  private static instance: ConfigService
  private cache: Map<string, any> = new Map()
  private db: DatabaseService

  private constructor() {
    super()
    this.db = DatabaseService.getInstance()
  }

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService()
    }
    return ConfigService.instance
  }

  public async initialize(): Promise<void> {
    // Load default configuration
    await this.setDefaults()
    
    // Load existing config from database
    await this.loadFromDatabase()
    
    console.log('✅ Configuration service initialized')
  }

  private async setDefaults(): Promise<void> {
    const defaults = {
      'app.theme': 'light',
      'app.language': 'en',
      'ai.defaultProvider': 'openai',
      'ai.openai.apiKey': '',
      'ai.openai.model': 'gpt-3.5-turbo',
      'ai.claude.apiKey': '',
      'ai.claude.model': 'claude-3-sonnet-20240229',
      'ai.gemini.apiKey': '',
      'ai.gemini.model': 'gemini-pro',
      'chat.maxHistory': 50,
      'chat.streamResponse': true,
      'image.defaultProvider': 'openai',
      'image.openai.model': 'dall-e-3',
      'image.defaultSize': '1024x1024',
      'plugins.enabled': true,
      'plugins.autoUpdate': false,
      'server.port': 3001,
      'security.encryptData': true
    }

    for (const [key, value] of Object.entries(defaults)) {
      if (!this.cache.has(key)) {
        this.cache.set(key, value)
      }
    }
  }

  private async loadFromDatabase(): Promise<void> {
    try {
      const configs = await this.db.all('SELECT key, value FROM config')
      for (const config of configs) {
        try {
          const value = JSON.parse(config.value)
          this.cache.set(config.key, value)
        } catch (e) {
          // If JSON parse fails, store as string
          this.cache.set(config.key, config.value)
        }
      }
    } catch (error) {
      console.warn('Failed to load config from database:', error)
    }
  }

  public async get<T = any>(key: string): Promise<T> {
    if (this.cache.has(key)) {
      return this.cache.get(key) as T
    }

    try {
      const result = await this.db.get('SELECT value FROM config WHERE key = ?', [key])
      if (result) {
        try {
          const value = JSON.parse(result.value)
          this.cache.set(key, value)
          return value as T
        } catch (e) {
          this.cache.set(key, result.value)
          return result.value as T
        }
      }
    } catch (error) {
      console.warn(`Failed to get config ${key}:`, error)
    }

    return undefined as T
  }

  public async set(key: string, value: any): Promise<void> {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value)
      
      await this.db.run(
        'INSERT OR REPLACE INTO config (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)',
        [key, stringValue]
      )
      
      this.cache.set(key, value)
      this.emit('configChanged', { key, value })
      
    } catch (error) {
      console.error(`Failed to set config ${key}:`, error)
      throw error
    }
  }

  public async delete(key: string): Promise<void> {
    try {
      await this.db.run('DELETE FROM config WHERE key = ?', [key])
      this.cache.delete(key)
      this.emit('configChanged', { key, value: undefined })
    } catch (error) {
      console.error(`Failed to delete config ${key}:`, error)
      throw error
    }
  }

  public async getAll(): Promise<ConfigValue> {
    const result: ConfigValue = {}
    
    try {
      const configs = await this.db.all('SELECT key, value FROM config')
      for (const config of configs) {
        try {
          result[config.key] = JSON.parse(config.value)
        } catch (e) {
          result[config.key] = config.value
        }
      }
    } catch (error) {
      console.warn('Failed to get all configs:', error)
    }

    // Add cached values that might not be in database yet
    for (const [key, value] of this.cache.entries()) {
      if (!(key in result)) {
        result[key] = value
      }
    }

    return result
  }

  public watch(key: string, callback: (value: any) => void): () => void {
    const handler = (data: { key: string; value: any }) => {
      if (data.key === key) {
        callback(data.value)
      }
    }
    
    this.on('configChanged', handler)
    
    return () => {
      this.removeListener('configChanged', handler)
    }
  }
}