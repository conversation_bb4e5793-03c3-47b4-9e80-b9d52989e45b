{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@main/*": ["src/main/*"], "@preload/*": ["src/preload/*"], "@renderer/*": ["src/renderer/src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx"], "references": [{"path": "./tsconfig.node.json"}]}