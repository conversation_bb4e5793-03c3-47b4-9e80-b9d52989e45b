import { EventEmitter } from 'events'
import { promises as fs } from 'fs'
import { join, resolve } from 'path'
import { app } from 'electron'
import { DatabaseService } from '../database/DatabaseService'
import { ConfigService } from '../services/ConfigService'

export interface PluginManifest {
  id: string
  name: string
  version: string
  description: string
  author: string
  main: string
  dependencies?: string[]
  permissions?: string[]
  config?: any
  hotReload?: boolean
}

export interface IPlugin {
  initialize(config: any): Promise<boolean>
  execute(request: any): Promise<any>
  cleanup(): Promise<void>
  getManifest(): PluginManifest
}

export interface PluginContext {
  config: ConfigService
  database: DatabaseService
  events: EventEmitter
  logger: (message: string, level?: 'info' | 'warn' | 'error') => void
}

export class PluginSystem extends EventEmitter {
  private static instance: PluginSystem
  private plugins: Map<string, IPlugin> = new Map()
  private pluginStates: Map<string, 'loaded' | 'unloaded' | 'error'> = new Map()
  private pluginConfigs: Map<string, any> = new Map()
  private pluginsDir: string
  private db: DatabaseService
  private config: ConfigService

  private constructor() {
    super()
    this.pluginsDir = join(app.getPath('userData'), 'plugins')
    this.db = DatabaseService.getInstance()
    this.config = ConfigService.getInstance()
    this.ensurePluginsDirectory()
  }

  public static getInstance(): PluginSystem {
    if (!PluginSystem.instance) {
      PluginSystem.instance = new PluginSystem()
    }
    return PluginSystem.instance
  }

  private async ensurePluginsDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.pluginsDir, { recursive: true })
    } catch (error) {
      console.error('Failed to create plugins directory:', error)
    }
  }

  public async discoverPlugins(): Promise<PluginManifest[]> {
    const manifests: PluginManifest[] = []

    try {
      const entries = await fs.readdir(this.pluginsDir, { withFileTypes: true })
      
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const manifestPath = join(this.pluginsDir, entry.name, 'manifest.json')
          
          try {
            const manifestContent = await fs.readFile(manifestPath, 'utf-8')
            const manifest: PluginManifest = JSON.parse(manifestContent)
            
            // Validate manifest
            if (this.validateManifest(manifest)) {
              manifests.push(manifest)
            } else {
              console.warn(`Invalid manifest for plugin: ${entry.name}`)
            }
          } catch (error) {
            console.warn(`Failed to read manifest for plugin: ${entry.name}`, error)
          }
        }
      }
    } catch (error) {
      console.error('Failed to discover plugins:', error)
    }

    return manifests
  }

  private validateManifest(manifest: any): manifest is PluginManifest {
    return (
      typeof manifest.id === 'string' &&
      typeof manifest.name === 'string' &&
      typeof manifest.version === 'string' &&
      typeof manifest.main === 'string'
    )
  }

  public async loadPlugin(pluginId: string): Promise<boolean> {
    try {
      if (this.plugins.has(pluginId)) {
        console.warn(`Plugin ${pluginId} is already loaded`)
        return true
      }

      const manifests = await this.discoverPlugins()
      const manifest = manifests.find(m => m.id === pluginId)
      
      if (!manifest) {
        throw new Error(`Plugin manifest not found: ${pluginId}`)
      }

      // Load plugin module
      const pluginPath = join(this.pluginsDir, pluginId, manifest.main)
      const PluginClass = await this.loadPluginModule(pluginPath)
      
      if (!PluginClass) {
        throw new Error(`Failed to load plugin class: ${pluginId}`)
      }

      // Create plugin instance
      const plugin = new PluginClass()
      
      // Validate plugin interface
      if (!this.validatePluginInterface(plugin)) {
        throw new Error(`Plugin does not implement required interface: ${pluginId}`)
      }

      // Get plugin config
      const config = await this.getPluginConfig(pluginId)
      
      // Initialize plugin in sandbox
      const success = await this.initializePluginSafely(plugin, config)
      
      if (success) {
        this.plugins.set(pluginId, plugin)
        this.pluginStates.set(pluginId, 'loaded')
        
        // Save to database
        await this.savePluginState(pluginId, true)
        
        this.emit('pluginLoaded', { pluginId, manifest })
        console.log(`✅ Plugin loaded: ${pluginId}`)
        return true
      } else {
        throw new Error(`Plugin initialization failed: ${pluginId}`)
      }
    } catch (error) {
      console.error(`Failed to load plugin ${pluginId}:`, error)
      this.pluginStates.set(pluginId, 'error')
      this.emit('pluginError', { pluginId, error: error.message })
      return false
    }
  }

  private async loadPluginModule(pluginPath: string): Promise<any> {
    try {
      // Clear require cache for hot reload
      delete require.cache[resolve(pluginPath)]
      
      const module = require(pluginPath)
      return module.default || module
    } catch (error) {
      console.error('Failed to load plugin module:', error)
      return null
    }
  }

  private validatePluginInterface(plugin: any): plugin is IPlugin {
    return (
      typeof plugin.initialize === 'function' &&
      typeof plugin.execute === 'function' &&
      typeof plugin.cleanup === 'function' &&
      typeof plugin.getManifest === 'function'
    )
  }

  private async initializePluginSafely(plugin: IPlugin, config: any): Promise<boolean> {
    try {
      // Create sandboxed context
      const context: PluginContext = {
        config: this.config,
        database: this.db,
        events: this,
        logger: (message: string, level: 'info' | 'warn' | 'error' = 'info') => {
          console.log(`[Plugin] ${level.toUpperCase()}: ${message}`)
        }
      }

      // Initialize with timeout
      const timeoutPromise = new Promise<boolean>((_, reject) => {
        setTimeout(() => reject(new Error('Plugin initialization timeout')), 30000)
      })

      const initPromise = plugin.initialize({ ...config, context })
      
      return await Promise.race([initPromise, timeoutPromise])
    } catch (error) {
      console.error('Plugin initialization failed:', error)
      return false
    }
  }

  public async unloadPlugin(pluginId: string): Promise<boolean> {
    try {
      const plugin = this.plugins.get(pluginId)
      
      if (!plugin) {
        console.warn(`Plugin ${pluginId} is not loaded`)
        return true
      }

      // Cleanup plugin
      await plugin.cleanup()
      
      // Remove from memory
      this.plugins.delete(pluginId)
      this.pluginStates.set(pluginId, 'unloaded')
      
      // Save to database
      await this.savePluginState(pluginId, false)
      
      this.emit('pluginUnloaded', { pluginId })
      console.log(`✅ Plugin unloaded: ${pluginId}`)
      return true
    } catch (error) {
      console.error(`Failed to unload plugin ${pluginId}:`, error)
      return false
    }
  }

  public async executePlugin(pluginId: string, request: any): Promise<any> {
    const plugin = this.plugins.get(pluginId)
    
    if (!plugin) {
      throw new Error(`Plugin not loaded: ${pluginId}`)
    }

    try {
      return await plugin.execute(request)
    } catch (error) {
      console.error(`Plugin execution failed for ${pluginId}:`, error)
      throw error
    }
  }

  public getLoadedPlugins(): string[] {
    return Array.from(this.plugins.keys())
  }

  public getPluginState(pluginId: string): 'loaded' | 'unloaded' | 'error' | 'unknown' {
    return this.pluginStates.get(pluginId) || 'unknown'
  }

  public isPluginLoaded(pluginId: string): boolean {
    return this.plugins.has(pluginId)
  }

  private async getPluginConfig(pluginId: string): Promise<any> {
    try {
      const config = await this.config.get(`plugins.${pluginId}`)
      return config || {}
    } catch (error) {
      console.warn(`Failed to get config for plugin ${pluginId}:`, error)
      return {}
    }
  }

  public async setPluginConfig(pluginId: string, config: any): Promise<void> {
    try {
      await this.config.set(`plugins.${pluginId}`, config)
      this.pluginConfigs.set(pluginId, config)
      
      // Reload plugin if it supports hot reload
      const manifests = await this.discoverPlugins()
      const manifest = manifests.find(m => m.id === pluginId)
      
      if (manifest?.hotReload && this.isPluginLoaded(pluginId)) {
        await this.unloadPlugin(pluginId)
        await this.loadPlugin(pluginId)
      }
    } catch (error) {
      console.error(`Failed to set config for plugin ${pluginId}:`, error)
      throw error
    }
  }

  private async savePluginState(pluginId: string, enabled: boolean): Promise<void> {
    try {
      await this.db.run(
        'INSERT OR REPLACE INTO plugins (id, name, version, enabled, updated_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)',
        [pluginId, pluginId, '1.0.0', enabled ? 1 : 0]
      )
    } catch (error) {
      console.error('Failed to save plugin state:', error)
    }
  }

  public async loadEnabledPlugins(): Promise<void> {
    try {
      const enabledPlugins = await this.db.all(
        'SELECT id FROM plugins WHERE enabled = 1'
      )

      for (const plugin of enabledPlugins) {
        await this.loadPlugin(plugin.id)
      }

      console.log(`✅ Loaded ${enabledPlugins.length} enabled plugins`)
    } catch (error) {
      console.error('Failed to load enabled plugins:', error)
    }
  }

  public async installPlugin(pluginPath: string): Promise<boolean> {
    try {
      // TODO: Implement plugin installation from file/URL
      console.log('Plugin installation not yet implemented')
      return false
    } catch (error) {
      console.error('Plugin installation failed:', error)
      return false
    }
  }

  public async uninstallPlugin(pluginId: string): Promise<boolean> {
    try {
      // Unload plugin first
      await this.unloadPlugin(pluginId)
      
      // Remove plugin directory
      const pluginDir = join(this.pluginsDir, pluginId)
      await fs.rmdir(pluginDir, { recursive: true })
      
      // Remove from database
      await this.db.run('DELETE FROM plugins WHERE id = ?', [pluginId])
      
      this.emit('pluginUninstalled', { pluginId })
      console.log(`✅ Plugin uninstalled: ${pluginId}`)
      return true
    } catch (error) {
      console.error(`Failed to uninstall plugin ${pluginId}:`, error)
      return false
    }
  }
}