export interface User {
  id: number
  username: string
  email: string
  created_at: string
  updated_at: string
}

export interface Conversation {
  id: string
  user_id: number
  title: string
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  conversation_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  metadata?: any
  created_at: string
}

export interface Plugin {
  id: string
  name: string
  version: string
  enabled: boolean
  config?: any
  created_at: string
  updated_at: string
}

export interface ImageGeneration {
  id: string
  user_id: number
  prompt: string
  model: string
  image_url?: string
  metadata?: any
  created_at: string
}

export interface ConfigValue {
  [key: string]: any
}

export interface WebSocketMessage {
  event: string
  data: any
}

export interface ChatStreamChunk {
  messageId: string
  conversationId: string
  chunk: string
  currentText: string
  isComplete: boolean
}

export interface ResearchProgress {
  researchId: string
  step: number
  totalSteps: number
  message: string
}

export interface ResearchResult {
  researchId: string
  query: string
  summary: string
  sources: Array<{
    title: string
    url: string
    snippet: string
  }>
  report: string
}