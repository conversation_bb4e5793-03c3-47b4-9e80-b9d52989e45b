import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { DatabaseService } from '../database/DatabaseService'

export interface User {
  id: number
  username: string
  email: string
  password_hash: string
  created_at: string
  updated_at: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
}

export interface LoginData {
  username: string
  password: string
}

export interface AuthResult {
  success: boolean
  user?: Omit<User, 'password_hash'>
  token?: string
  message?: string
}

export class AuthService {
  private static instance: AuthService
  private db: DatabaseService
  private jwtSecret: string

  private constructor() {
    this.db = DatabaseService.getInstance()
    this.jwtSecret = process.env.JWT_SECRET || 'jimu-default-secret-key'
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService()
    }
    return AuthService.instance
  }

  public async register(userData: RegisterData): Promise<AuthResult> {
    try {
      // Check if user already exists
      const existingUser = await this.db.get(
        'SELECT id FROM users WHERE username = ? OR email = ?',
        [userData.username, userData.email]
      )

      if (existingUser) {
        return {
          success: false,
          message: 'Username or email already exists'
        }
      }

      // Hash password
      const saltRounds = 12
      const passwordHash = await bcrypt.hash(userData.password, saltRounds)

      // Create user
      const result = await this.db.run(
        'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
        [userData.username, userData.email, passwordHash]
      )

      // Get created user
      const newUser = await this.db.get(
        'SELECT id, username, email, created_at, updated_at FROM users WHERE id = ?',
        [result.id]
      )

      // Generate token
      const token = this.generateToken(newUser)

      return {
        success: true,
        user: newUser,
        token,
        message: 'User registered successfully'
      }
    } catch (error) {
      console.error('Registration error:', error)
      return {
        success: false,
        message: 'Registration failed'
      }
    }
  }

  public async login(credentials: LoginData): Promise<AuthResult> {
    try {
      // Find user
      const user = await this.db.get(
        'SELECT * FROM users WHERE username = ?',
        [credentials.username]
      )

      if (!user) {
        return {
          success: false,
          message: 'Invalid username or password'
        }
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(credentials.password, user.password_hash)

      if (!isValidPassword) {
        return {
          success: false,
          message: 'Invalid username or password'
        }
      }

      // Generate token
      const userWithoutPassword = {
        id: user.id,
        username: user.username,
        email: user.email,
        created_at: user.created_at,
        updated_at: user.updated_at
      }

      const token = this.generateToken(userWithoutPassword)

      return {
        success: true,
        user: userWithoutPassword,
        token,
        message: 'Login successful'
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: 'Login failed'
      }
    }
  }

  public async verifyToken(token: string): Promise<User | null> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any
      
      const user = await this.db.get(
        'SELECT id, username, email, created_at, updated_at FROM users WHERE id = ?',
        [decoded.id]
      )

      return user || null
    } catch (error) {
      return null
    }
  }

  public async refreshToken(token: string): Promise<string | null> {
    try {
      const user = await this.verifyToken(token)
      if (!user) return null

      return this.generateToken(user)
    } catch (error) {
      return null
    }
  }

  public async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const user = await this.db.get(
        'SELECT password_hash FROM users WHERE id = ?',
        [userId]
      )

      if (!user) return false

      const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash)
      if (!isValidPassword) return false

      const newPasswordHash = await bcrypt.hash(newPassword, 12)
      
      await this.db.run(
        'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [newPasswordHash, userId]
      )

      return true
    } catch (error) {
      console.error('Change password error:', error)
      return false
    }
  }

  public async deleteUser(userId: number): Promise<boolean> {
    try {
      await this.db.run('DELETE FROM users WHERE id = ?', [userId])
      return true
    } catch (error) {
      console.error('Delete user error:', error)
      return false
    }
  }

  private generateToken(user: Omit<User, 'password_hash'>): string {
    return jwt.sign(
      { 
        id: user.id, 
        username: user.username, 
        email: user.email 
      },
      this.jwtSecret,
      { expiresIn: '7d' }
    )
  }
}