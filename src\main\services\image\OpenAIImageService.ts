import OpenAI from 'openai'
import { ImageService, ImageGenerationParams, ImageResult, ImageServiceConfig } from './ImageService'

export class OpenAIImageService extends ImageService {
  private client: OpenAI

  constructor(config: ImageServiceConfig) {
    super(config, 'OpenAI DALL-E')
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL
    })
  }

  async generateImage(params: ImageGenerationParams): Promise<ImageResult> {
    try {
      const response = await this.client.images.generate({
        model: params.model || this.config.model || 'dall-e-3',
        prompt: params.prompt,
        size: params.size as any || this.config.defaultSize as any || '1024x1024',
        quality: params.quality || this.config.defaultQuality || 'standard',
        style: params.style || 'vivid',
        n: params.n || 1,
        response_format: 'url'
      })

      const image = response.data[0]
      if (!image?.url) {
        throw new Error('No image URL returned from OpenAI')
      }

      return {
        id: `dalle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        url: image.url,
        revisedPrompt: image.revised_prompt,
        size: params.size || this.config.defaultSize || '1024x1024',
        model: params.model || this.config.model || 'dall-e-3',
        created: response.created
      }
    } catch (error) {
      throw this.handleError(error, 'Image generation')
    }
  }

  getSupportedModels(): string[] {
    return ['dall-e-3', 'dall-e-2']
  }

  getSupportedSizes(): string[] {
    return [
      '1024x1024',
      '1024x1792',
      '1792x1024',
      '512x512',  // DALL-E 2 only
      '256x256'   // DALL-E 2 only
    ]
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test with a simple generation request
      await this.client.images.generate({
        model: 'dall-e-2',
        prompt: 'test',
        size: '256x256',
        n: 1
      })
      return true
    } catch (error) {
      console.error('OpenAI Image service validation failed:', error)
      return false
    }
  }

  updateConfig(newConfig: Partial<ImageServiceConfig>): void {
    super.updateConfig(newConfig)
    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseURL
    })
  }
}