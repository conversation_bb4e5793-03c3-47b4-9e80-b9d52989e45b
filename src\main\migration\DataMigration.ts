import { promises as fs } from 'fs'
import { join } from 'path'
import { app } from 'electron'
import { DatabaseService } from '../database/DatabaseService'
import { ConfigService } from '../services/ConfigService'

export interface MigrationData {
  conversations: Array<{
    id: string
    title: string
    messages: Array<{
      role: string
      content: string
      timestamp: string
    }>
    created_at: string
    updated_at: string
  }>
  config: { [key: string]: any }
  images: Array<{
    id: string
    prompt: string
    url: string
    metadata: any
    created_at: string
  }>
  plugins: Array<{
    id: string
    name: string
    enabled: boolean
    config: any
  }>
}

export class DataMigration {
  private db: DatabaseService
  private config: ConfigService
  private migrationDir: string

  constructor() {
    this.db = DatabaseService.getInstance()
    this.config = ConfigService.getInstance()
    this.migrationDir = join(app.getPath('userData'), 'migration')
  }

  public async exportData(): Promise<string> {
    try {
      await fs.mkdir(this.migrationDir, { recursive: true })

      const data: MigrationData = {
        conversations: await this.exportConversations(),
        config: await this.exportConfig(),
        images: await this.exportImages(),
        plugins: await this.exportPlugins()
      }

      const exportPath = join(this.migrationDir, `jimu_export_${Date.now()}.json`)
      await fs.writeFile(exportPath, JSON.stringify(data, null, 2))

      console.log(`✅ Data exported to: ${exportPath}`)
      return exportPath
    } catch (error) {
      console.error('Failed to export data:', error)
      throw error
    }
  }

  public async importData(filePath: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      const data: MigrationData = JSON.parse(content)

      await this.importConversations(data.conversations)
      await this.importConfig(data.config)
      await this.importImages(data.images)
      await this.importPlugins(data.plugins)

      console.log('✅ Data imported successfully')
    } catch (error) {
      console.error('Failed to import data:', error)
      throw error
    }
  }

  private async exportConversations(): Promise<MigrationData['conversations']> {
    try {
      const conversations = await this.db.all('SELECT * FROM conversations ORDER BY created_at DESC')
      const result: MigrationData['conversations'] = []

      for (const conv of conversations) {
        const messages = await this.db.all(
          'SELECT role, content, created_at as timestamp FROM messages WHERE conversation_id = ? ORDER BY created_at ASC',
          [conv.id]
        )

        result.push({
          id: conv.id,
          title: conv.title,
          messages,
          created_at: conv.created_at,
          updated_at: conv.updated_at
        })
      }

      return result
    } catch (error) {
      console.warn('Failed to export conversations:', error)
      return []
    }
  }

  private async exportConfig(): Promise<{ [key: string]: any }> {
    try {
      return await this.config.getAll()
    } catch (error) {
      console.warn('Failed to export config:', error)
      return {}
    }
  }

  private async exportImages(): Promise<MigrationData['images']> {
    try {
      const images = await this.db.all('SELECT * FROM image_generations ORDER BY created_at DESC')
      return images.map(img => ({
        id: img.id,
        prompt: img.prompt,
        url: img.image_url,
        metadata: img.metadata ? JSON.parse(img.metadata) : {},
        created_at: img.created_at
      }))
    } catch (error) {
      console.warn('Failed to export images:', error)
      return []
    }
  }

  private async exportPlugins(): Promise<MigrationData['plugins']> {
    try {
      const plugins = await this.db.all('SELECT * FROM plugins')
      return plugins.map(plugin => ({
        id: plugin.id,
        name: plugin.name,
        enabled: plugin.enabled === 1,
        config: plugin.config ? JSON.parse(plugin.config) : {}
      }))
    } catch (error) {
      console.warn('Failed to export plugins:', error)
      return []
    }
  }

  private async importConversations(conversations: MigrationData['conversations']): Promise<void> {
    for (const conv of conversations) {
      try {
        // Create conversation
        await this.db.run(
          'INSERT OR REPLACE INTO conversations (id, user_id, title, created_at, updated_at) VALUES (?, ?, ?, ?, ?)',
          [conv.id, 1, conv.title, conv.created_at, conv.updated_at]
        )

        // Import messages
        for (const msg of conv.messages) {
          const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          await this.db.run(
            'INSERT INTO messages (id, conversation_id, role, content, created_at) VALUES (?, ?, ?, ?, ?)',
            [messageId, conv.id, msg.role, msg.content, msg.timestamp]
          )
        }
      } catch (error) {
        console.warn(`Failed to import conversation ${conv.id}:`, error)
      }
    }
  }

  private async importConfig(configData: { [key: string]: any }): Promise<void> {
    for (const [key, value] of Object.entries(configData)) {
      try {
        await this.config.set(key, value)
      } catch (error) {
        console.warn(`Failed to import config ${key}:`, error)
      }
    }
  }

  private async importImages(images: MigrationData['images']): Promise<void> {
    for (const img of images) {
      try {
        await this.db.run(
          'INSERT OR REPLACE INTO image_generations (id, user_id, prompt, model, image_url, metadata, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [img.id, 1, img.prompt, 'imported', img.url, JSON.stringify(img.metadata), img.created_at]
        )
      } catch (error) {
        console.warn(`Failed to import image ${img.id}:`, error)
      }
    }
  }

  private async importPlugins(plugins: MigrationData['plugins']): Promise<void> {
    for (const plugin of plugins) {
      try {
        await this.db.run(
          'INSERT OR REPLACE INTO plugins (id, name, version, enabled, config, created_at, updated_at) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)',
          [plugin.id, plugin.name, '1.0.0', plugin.enabled ? 1 : 0, JSON.stringify(plugin.config)]
        )
      } catch (error) {
        console.warn(`Failed to import plugin ${plugin.id}:`, error)
      }
    }
  }

  public async createPythonExportScript(): Promise<string> {
    const scriptContent = `#!/usr/bin/env python3
"""
JIMU Data Export Script
Exports data from the old Python-based JIMU system for migration to the new Node.js version.
"""

import json
import sqlite3
import os
from datetime import datetime
from pathlib import Path

def export_jimu_data(db_path: str, output_path: str):
    """Export JIMU data to JSON format for migration."""
    
    if not os.path.exists(db_path):
        print(f"Database not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        data = {
            "conversations": [],
            "config": {},
            "images": [],
            "plugins": [],
            "export_timestamp": datetime.now().isoformat()
        }
        
        # Export conversations
        try:
            cursor.execute("SELECT * FROM conversations ORDER BY created_at DESC")
            conversations = cursor.fetchall()
            
            for conv in conversations:
                cursor.execute(
                    "SELECT role, content, created_at FROM messages WHERE conversation_id = ? ORDER BY created_at ASC",
                    (conv['id'],)
                )
                messages = cursor.fetchall()
                
                data["conversations"].append({
                    "id": conv['id'],
                    "title": conv['title'],
                    "messages": [dict(msg) for msg in messages],
                    "created_at": conv['created_at'],
                    "updated_at": conv['updated_at']
                })
        except Exception as e:
            print(f"Warning: Failed to export conversations: {e}")
        
        # Export configuration
        try:
            cursor.execute("SELECT key, value FROM config")
            config_rows = cursor.fetchall()
            for row in config_rows:
                try:
                    data["config"][row['key']] = json.loads(row['value'])
                except:
                    data["config"][row['key']] = row['value']
        except Exception as e:
            print(f"Warning: Failed to export config: {e}")
        
        # Export images
        try:
            cursor.execute("SELECT * FROM image_generations ORDER BY created_at DESC")
            images = cursor.fetchall()
            for img in images:
                metadata = {}
                try:
                    if img['metadata']:
                        metadata = json.loads(img['metadata'])
                except:
                    pass
                
                data["images"].append({
                    "id": img['id'],
                    "prompt": img['prompt'],
                    "url": img['image_url'],
                    "metadata": metadata,
                    "created_at": img['created_at']
                })
        except Exception as e:
            print(f"Warning: Failed to export images: {e}")
        
        # Export plugins
        try:
            cursor.execute("SELECT * FROM plugins")
            plugins = cursor.fetchall()
            for plugin in plugins:
                config = {}
                try:
                    if plugin['config']:
                        config = json.loads(plugin['config'])
                except:
                    pass
                
                data["plugins"].append({
                    "id": plugin['id'],
                    "name": plugin['name'],
                    "enabled": bool(plugin['enabled']),
                    "config": config
                })
        except Exception as e:
            print(f"Warning: Failed to export plugins: {e}")
        
        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        conn.close()
        
        print(f"✅ Data exported successfully to: {output_path}")
        print(f"   Conversations: {len(data['conversations'])}")
        print(f"   Config entries: {len(data['config'])}")
        print(f"   Images: {len(data['images'])}")
        print(f"   Plugins: {len(data['plugins'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Export failed: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 3:
        print("Usage: python export_jimu_data.py <database_path> <output_path>")
        print("Example: python export_jimu_data.py ~/.jimu/jimu.db jimu_export.json")
        sys.exit(1)
    
    db_path = sys.argv[1]
    output_path = sys.argv[2]
    
    success = export_jimu_data(db_path, output_path)
    sys.exit(0 if success else 1)
`

    const scriptPath = join(this.migrationDir, 'export_jimu_data.py')
    await fs.writeFile(scriptPath, scriptContent)
    await fs.chmod(scriptPath, 0o755)

    console.log(`✅ Python export script created: ${scriptPath}`)
    return scriptPath
  }
}