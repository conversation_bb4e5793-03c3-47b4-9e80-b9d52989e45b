import React, { useState, useEffect } from 'react'
import { Layout, ConfigProvider, theme } from 'antd'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import Sidebar from './components/Sidebar'
import ChatPage from './pages/ChatPage'
import ImagePage from './pages/ImagePage'
import PluginsPage from './pages/PluginsPage'
import ResearchPage from './pages/ResearchPage'
import SettingsPage from './pages/SettingsPage'
import { WebSocketProvider } from './services/WebSocketService'
import { ConfigProvider as AppConfigProvider } from './services/ConfigService'
import './App.css'

const { Content } = Layout

function App() {
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('light')
  const [collapsed, setCollapsed] = useState(false)

  useEffect(() => {
    // Load theme from config
    const loadTheme = async () => {
      try {
        const savedTheme = await window.api?.config?.get('app.theme')
        if (savedTheme) {
          setCurrentTheme(savedTheme)
        }
      } catch (error) {
        console.warn('Failed to load theme:', error)
      }
    }
    
    loadTheme()
  }, [])

  const toggleTheme = () => {
    const newTheme = currentTheme === 'light' ? 'dark' : 'light'
    setCurrentTheme(newTheme)
    window.api?.config?.set('app.theme', newTheme)
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: currentTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
      }}
    >
      <AppConfigProvider>
        <WebSocketProvider>
          <Router>
            <Layout style={{ height: '100vh' }}>
              <Sidebar 
                collapsed={collapsed}
                onCollapse={setCollapsed}
                currentTheme={currentTheme}
                onThemeToggle={toggleTheme}
              />
              <Layout style={{ marginLeft: collapsed ? 80 : 250 }}>
                <Content style={{ margin: 0, overflow: 'auto' }}>
                  <Routes>
                    <Route path="/" element={<Navigate to="/chat" replace />} />
                    <Route path="/chat" element={<ChatPage />} />
                    <Route path="/chat/:conversationId" element={<ChatPage />} />
                    <Route path="/image" element={<ImagePage />} />
                    <Route path="/plugins" element={<PluginsPage />} />
                    <Route path="/research" element={<ResearchPage />} />
                    <Route path="/settings" element={<SettingsPage />} />
                  </Routes>
                </Content>
              </Layout>
            </Layout>
          </Router>
        </WebSocketProvider>
      </AppConfigProvider>
    </ConfigProvider>
  )
}

export default App