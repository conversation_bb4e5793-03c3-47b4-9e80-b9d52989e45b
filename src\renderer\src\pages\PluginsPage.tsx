import React, { useState, useEffect } from 'react'
import { <PERSON>, List, Switch, Button, Tag, Typography, Space, Divider } from 'antd'
import { AppstoreOutlined, SettingOutlined, ReloadOutlined } from '@ant-design/icons'

const { Title, Text, Paragraph } = Typography

interface Plugin {
  id: string
  name: string
  version: string
  enabled: boolean
  config?: any
  created_at: string
  updated_at: string
}

function PluginsPage() {
  const [plugins, setPlugins] = useState<Plugin[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadPlugins()
  }, [])

  const loadPlugins = async () => {
    try {
      setLoading(true)
      const response = await fetch('http://localhost:3001/api/plugins')
      if (response.ok) {
        const data = await response.json()
        setPlugins(data)
      }
    } catch (error) {
      console.error('Failed to load plugins:', error)
    } finally {
      setLoading(false)
    }
  }

  const togglePlugin = async (pluginId: string, enabled: boolean) => {
    try {
      const response = await fetch(`http://localhost:3001/api/plugins/${pluginId}/toggle`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled })
      })
      
      if (response.ok) {
        setPlugins(prev => prev.map(plugin => 
          plugin.id === pluginId ? { ...plugin, enabled } : plugin
        ))
      }
    } catch (error) {
      console.error('Failed to toggle plugin:', error)
    }
  }

  // Mock plugins data if empty
  const mockPlugins: Plugin[] = [
    {
      id: 'deer-flow',
      name: 'Deer Flow',
      version: '1.0.0',
      enabled: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'research-agent',
      name: 'Research Agent',
      version: '1.2.0',
      enabled: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'ai-programming',
      name: 'AI Programming Agent',
      version: '0.9.0',
      enabled: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ]

  const displayPlugins = plugins.length > 0 ? plugins : mockPlugins

  return (
    <div style={{ padding: '24px', height: '100vh', overflow: 'auto' }}>
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>Plugin Management</Title>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={loadPlugins}
          loading={loading}
        >
          Refresh
        </Button>
      </div>

      <Card style={{ marginBottom: '24px' }}>
        <Paragraph>
          Plugins extend JIMU's functionality with additional features and capabilities. 
          You can enable or disable plugins as needed. Some plugins may require additional configuration.
        </Paragraph>
      </Card>

      <List
        grid={{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 3 }}
        dataSource={displayPlugins}
        loading={loading}
        renderItem={(plugin) => (
          <List.Item>
            <Card
              title={
                <Space>
                  <AppstoreOutlined />
                  {plugin.name}
                  <Tag color={plugin.enabled ? 'green' : 'default'}>
                    {plugin.enabled ? 'Enabled' : 'Disabled'}
                  </Tag>
                </Space>
              }
              extra={
                <Switch
                  checked={plugin.enabled}
                  onChange={(checked) => togglePlugin(plugin.id, checked)}
                />
              }
              actions={[
                <Button 
                  type="text" 
                  icon={<SettingOutlined />}
                  disabled={!plugin.enabled}
                >
                  Configure
                </Button>
              ]}
            >
              <div style={{ marginBottom: '12px' }}>
                <Text strong>Version:</Text> <Text code>{plugin.version}</Text>
              </div>
              
              <div style={{ marginBottom: '12px' }}>
                <Text strong>Description:</Text>
                <Paragraph style={{ margin: '4px 0 0 0', color: '#666' }}>
                  {getPluginDescription(plugin.id)}
                </Paragraph>
              </div>

              <Divider style={{ margin: '12px 0' }} />
              
              <div style={{ fontSize: '12px', color: '#999' }}>
                <div>Created: {new Date(plugin.created_at).toLocaleDateString()}</div>
                <div>Updated: {new Date(plugin.updated_at).toLocaleDateString()}</div>
              </div>
            </Card>
          </List.Item>
        )}
      />

      {displayPlugins.length === 0 && !loading && (
        <Card style={{ textAlign: 'center', padding: '50px' }}>
          <AppstoreOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
          <Title level={4} style={{ color: '#999' }}>No Plugins Found</Title>
          <Paragraph style={{ color: '#999' }}>
            No plugins are currently installed. You can install plugins from the marketplace or develop your own.
          </Paragraph>
        </Card>
      )}
    </div>
  )
}

function getPluginDescription(pluginId: string): string {
  const descriptions: Record<string, string> = {
    'deer-flow': 'Advanced workflow automation and content generation system with support for research, writing, and presentation creation.',
    'research-agent': 'Intelligent research assistant that can search, analyze, and synthesize information from multiple sources.',
    'ai-programming': 'AI-powered programming assistant for code generation, debugging, and optimization across multiple languages.',
    'data-analysis': 'Comprehensive data analysis tools with visualization capabilities and statistical computing features.',
    'openmanus-agent': 'Integration with OpenManus platform for enhanced AI capabilities and extended functionality.'
  }
  
  return descriptions[pluginId] || 'A powerful plugin that extends JIMU\'s capabilities with additional features and functionality.'
}

export default PluginsPage