import React, { useState, useEffect, useRef } from 'react'
import { Layout, Input, Button, List, Typography, Space, Spin, Card } from 'antd'
import { SendOutlined, PlusOutlined } from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'
import { useWebSocket } from '../services/WebSocketService'

const { Content, Sider } = Layout
const { TextArea } = Input
const { Text, Paragraph } = Typography

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: string
}

interface Conversation {
  id: string
  title: string
  updated_at: string
}

function ChatPage() {
  const { conversationId } = useParams()
  const navigate = useNavigate()
  const { sendMessage, subscribe, connected } = useWebSocket()
  
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [currentConversation, setCurrentConversation] = useState<string | null>(conversationId || null)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [loading, setLoading] = useState(false)
  const [streamingMessage, setStreamingMessage] = useState<string>('')
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadConversations()
  }, [])

  useEffect(() => {
    if (conversationId && conversationId !== currentConversation) {
      setCurrentConversation(conversationId)
      loadMessages(conversationId)
    }
  }, [conversationId])

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  useEffect(() => {
    // Subscribe to WebSocket events
    const unsubscribers = [
      subscribe('chat:messageSaved', handleMessageSaved),
      subscribe('chat:streamChunk', handleStreamChunk),
      subscribe('chat:error', handleChatError)
    ]

    return () => {
      unsubscribers.forEach(unsub => unsub())
    }
  }, [])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const loadConversations = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/conversations')
      if (response.ok) {
        const data = await response.json()
        setConversations(data)
        
        // If no current conversation and we have conversations, select the first one
        if (!currentConversation && data.length > 0) {
          const firstConv = data[0]
          setCurrentConversation(firstConv.id)
          navigate(`/chat/${firstConv.id}`)
          loadMessages(firstConv.id)
        }
      }
    } catch (error) {
      console.error('Failed to load conversations:', error)
    }
  }

  const loadMessages = async (convId: string) => {
    try {
      const response = await fetch(`http://localhost:3001/api/conversations/${convId}/messages`)
      if (response.ok) {
        const data = await response.json()
        setMessages(data)
      }
    } catch (error) {
      console.error('Failed to load messages:', error)
    }
  }

  const createNewConversation = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: 'New Conversation', userId: 1 })
      })
      
      if (response.ok) {
        const data = await response.json()
        const newConvId = data.conversationId
        
        await loadConversations()
        setCurrentConversation(newConvId)
        navigate(`/chat/${newConvId}`)
        setMessages([])
      }
    } catch (error) {
      console.error('Failed to create conversation:', error)
    }
  }

  const handleSendMessage = () => {
    if (!inputValue.trim() || !currentConversation || !connected) return

    setLoading(true)
    setStreamingMessage('')
    setStreamingMessageId(null)

    // Add user message to UI immediately
    const userMessage: Message = {
      id: `temp_${Date.now()}`,
      role: 'user',
      content: inputValue,
      timestamp: new Date().toISOString()
    }
    setMessages(prev => [...prev, userMessage])

    // Send to backend
    sendMessage('chat:message', {
      conversationId: currentConversation,
      message: inputValue,
      role: 'user'
    })

    setInputValue('')
  }

  const handleMessageSaved = (data: any) => {
    setLoading(false)
    // Message is already in the UI, just update with real ID if needed
  }

  const handleStreamChunk = (data: any) => {
    const { messageId, chunk, currentText, isComplete } = data
    
    if (streamingMessageId !== messageId) {
      setStreamingMessageId(messageId)
      setStreamingMessage('')
    }
    
    setStreamingMessage(currentText)
    
    if (isComplete) {
      // Add final message to messages list
      const assistantMessage: Message = {
        id: messageId,
        role: 'assistant',
        content: currentText,
        timestamp: new Date().toISOString()
      }
      setMessages(prev => [...prev, assistantMessage])
      setStreamingMessage('')
      setStreamingMessageId(null)
      setLoading(false)
    }
  }

  const handleChatError = (data: any) => {
    console.error('Chat error:', data.error)
    setLoading(false)
    setStreamingMessage('')
    setStreamingMessageId(null)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  return (
    <Layout style={{ height: '100vh' }}>
      <Sider width={300} style={{ background: '#fafafa', borderRight: '1px solid #f0f0f0' }}>
        <div style={{ padding: '16px' }}>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={createNewConversation}
            style={{ width: '100%', marginBottom: '16px' }}
          >
            New Chat
          </Button>
          
          <List
            dataSource={conversations}
            renderItem={(conv) => (
              <List.Item
                style={{
                  padding: '8px 12px',
                  cursor: 'pointer',
                  backgroundColor: conv.id === currentConversation ? '#e6f7ff' : 'transparent',
                  borderRadius: '6px',
                  marginBottom: '4px'
                }}
                onClick={() => {
                  setCurrentConversation(conv.id)
                  navigate(`/chat/${conv.id}`)
                  loadMessages(conv.id)
                }}
              >
                <div style={{ width: '100%' }}>
                  <Text ellipsis style={{ fontSize: '14px' }}>
                    {conv.title || 'Untitled'}
                  </Text>
                  <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                    {new Date(conv.updated_at).toLocaleDateString()}
                  </div>
                </div>
              </List.Item>
            )}
          />
        </div>
      </Sider>
      
      <Content style={{ display: 'flex', flexDirection: 'column' }}>
        <div style={{ 
          flex: 1, 
          padding: '16px', 
          overflowY: 'auto',
          background: '#fff'
        }}>
          {messages.map((message) => (
            <Card
              key={message.id}
              style={{
                marginBottom: '16px',
                backgroundColor: message.role === 'user' ? '#f0f8ff' : '#f9f9f9'
              }}
              bodyStyle={{ padding: '12px 16px' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text strong style={{ color: message.role === 'user' ? '#1890ff' : '#52c41a' }}>
                  {message.role === 'user' ? 'You' : 'Assistant'}
                </Text>
                <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                  {message.content}
                </Paragraph>
              </Space>
            </Card>
          ))}
          
          {streamingMessage && (
            <Card
              style={{ marginBottom: '16px', backgroundColor: '#f9f9f9' }}
              bodyStyle={{ padding: '12px 16px' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Text strong style={{ color: '#52c41a' }}>Assistant</Text>
                <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                  {streamingMessage}
                  <span style={{ animation: 'blink 1s infinite' }}>|</span>
                </Paragraph>
              </Space>
            </Card>
          )}
          
          {loading && !streamingMessage && (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size="large" />
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
        
        <div style={{ 
          padding: '16px', 
          borderTop: '1px solid #f0f0f0',
          background: '#fff'
        }}>
          <Space.Compact style={{ width: '100%' }}>
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message... (Press Enter to send, Shift+Enter for new line)"
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={!connected || !currentConversation}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSendMessage}
              disabled={!inputValue.trim() || loading || !connected || !currentConversation}
              style={{ height: 'auto' }}
            />
          </Space.Compact>
          
          {!connected && (
            <Text type="danger" style={{ fontSize: '12px', marginTop: '8px', display: 'block' }}>
              Disconnected from server
            </Text>
          )}
        </div>
      </Content>
    </Layout>
  )
}

export default ChatPage