import { EventEmitter } from 'events'
import { SearchManager, TavilySearchService, GoogleSearchService, BingSearchService } from './SearchService'
import { WorkflowEngine, SearchStepProcessor, AnalyzeStepProcessor, SynthesizeStepProcessor, GenerateStepProcessor, Workflow } from './WorkflowEngine'
import { AIManager } from '../ai/AIManager'
import { ConfigService } from '../ConfigService'

export interface ResearchRequest {
  query: string
  options?: {
    maxSources?: number
    language?: string
    region?: string
    depth?: 'basic' | 'standard' | 'comprehensive'
    format?: 'markdown' | 'html' | 'json'
  }
}

export interface ResearchResult {
  id: string
  query: string
  summary: string
  sources: Array<{
    title: string
    url: string
    snippet: string
    relevance?: number
  }>
  report: string
  metadata: {
    duration: number
    sourcesFound: number
    workflowSteps: number
    model: string
  }
}

export class ResearchAgent extends EventEmitter {
  private static instance: ResearchAgent
  private searchManager: SearchManager
  private workflowEngine: WorkflowEngine
  private aiManager: AIManager
  private config: ConfigService
  private isInitialized: boolean = false

  private constructor() {
    super()
    this.searchManager = new SearchManager()
    this.workflowEngine = new WorkflowEngine()
    this.aiManager = AIManager.getInstance()
    this.config = ConfigService.getInstance()
  }

  public static getInstance(): ResearchAgent {
    if (!ResearchAgent.instance) {
      ResearchAgent.instance = new ResearchAgent()
    }
    return ResearchAgent.instance
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // Initialize search services based on configuration
      await this.initializeSearchServices()
      
      // Register workflow processors
      this.registerWorkflowProcessors()
      
      this.isInitialized = true
      console.log('✅ Research Agent initialized successfully')
    } catch (error) {
      console.error('Failed to initialize Research Agent:', error)
      throw error
    }
  }

  private async initializeSearchServices(): Promise<void> {
    // Initialize Tavily search (primary)
    const tavilyApiKey = await this.config.get<string>('search.tavily.apiKey')
    if (tavilyApiKey) {
      this.searchManager.addService(new TavilySearchService(tavilyApiKey))
    }

    // Initialize Google search (secondary)
    const googleApiKey = await this.config.get<string>('search.google.apiKey')
    const googleSearchEngineId = await this.config.get<string>('search.google.searchEngineId')
    if (googleApiKey && googleSearchEngineId) {
      this.searchManager.addService(new GoogleSearchService(googleApiKey, googleSearchEngineId))
    }

    // Initialize Bing search (tertiary)
    const bingApiKey = await this.config.get<string>('search.bing.apiKey')
    if (bingApiKey) {
      this.searchManager.addService(new BingSearchService(bingApiKey))
    }

    const configuredServices = this.searchManager.getConfiguredServices()
    if (configuredServices.length === 0) {
      console.warn('No search services configured. Research functionality will be limited.')
    } else {
      console.log(`Configured search services: ${configuredServices.join(', ')}`)
    }
  }

  private registerWorkflowProcessors(): void {
    this.workflowEngine.registerProcessor(new SearchStepProcessor(this.searchManager))
    this.workflowEngine.registerProcessor(new AnalyzeStepProcessor(this.aiManager))
    this.workflowEngine.registerProcessor(new SynthesizeStepProcessor(this.aiManager))
    this.workflowEngine.registerProcessor(new GenerateStepProcessor(this.aiManager))
  }

  public async research(request: ResearchRequest): Promise<ResearchResult> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const researchId = `research_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const startTime = Date.now()

    try {
      // Create workflow based on research depth
      const workflow = this.createResearchWorkflow(request)
      
      // Execute workflow with progress tracking
      const workflowResult = await this.workflowEngine.execute(workflow, {
        input: { query: request.query, options: request.options },
        onProgress: (step: string, progress: number, message: string) => {
          this.emit('progress', { researchId, step, progress, message })
        },
        onStepComplete: (stepId: string, result: any) => {
          this.emit('stepComplete', { researchId, stepId, result })
        }
      })

      // Extract results
      const searchResult = workflowResult.results.search
      const analysisResult = workflowResult.results.analyze
      const synthesisResult = workflowResult.results.synthesize
      const reportResult = workflowResult.results.generate

      const result: ResearchResult = {
        id: researchId,
        query: request.query,
        summary: synthesisResult?.synthesis || 'No summary available',
        sources: searchResult?.results?.map((source: any, index: number) => ({
          ...source,
          relevance: this.calculateRelevance(source, request.query)
        })) || [],
        report: reportResult?.report || 'No report generated',
        metadata: {
          duration: Date.now() - startTime,
          sourcesFound: searchResult?.results?.length || 0,
          workflowSteps: workflow.steps.length,
          model: analysisResult?.metadata?.model || 'unknown'
        }
      }

      this.emit('complete', { researchId, result })
      return result
    } catch (error) {
      this.emit('error', { researchId, error: error.message })
      throw error
    }
  }

  private createResearchWorkflow(request: ResearchRequest): Workflow {
    const depth = request.options?.depth || 'standard'
    const maxSources = request.options?.maxSources || this.getDefaultMaxSources(depth)
    const format = request.options?.format || 'markdown'

    const workflow: Workflow = {
      id: `research_workflow_${depth}`,
      name: `Research Workflow (${depth})`,
      description: `Automated research workflow for ${depth} analysis`,
      steps: [
        {
          id: 'search',
          type: 'search',
          name: 'Search Information',
          config: {
            query: '{{query}}',
            maxResults: maxSources
          }
        },
        {
          id: 'analyze',
          type: 'analyze',
          name: 'Analyze Sources',
          config: {
            inputStep: 'search',
            prompt: this.getAnalysisPrompt(depth)
          },
          dependencies: ['search']
        },
        {
          id: 'synthesize',
          type: 'synthesize',
          name: 'Synthesize Information',
          config: {
            inputSteps: ['search', 'analyze'],
            prompt: this.getSynthesisPrompt(depth)
          },
          dependencies: ['search', 'analyze']
        },
        {
          id: 'generate',
          type: 'generate',
          name: 'Generate Report',
          config: {
            inputStep: 'synthesize',
            format: format,
            prompt: this.getGenerationPrompt(depth, format)
          },
          dependencies: ['synthesize']
        }
      ]
    }

    return workflow
  }

  private getDefaultMaxSources(depth: string): number {
    switch (depth) {
      case 'basic': return 5
      case 'standard': return 10
      case 'comprehensive': return 20
      default: return 10
    }
  }

  private getAnalysisPrompt(depth: string): string {
    const basePrompt = `Analyze the following search results for the query "{{query}}". Extract key information, identify main themes, and assess the credibility of sources.

Search Results:
{{input}}

Please provide:
1. Key findings and main themes
2. Source credibility assessment
3. Information gaps or contradictions
4. Relevance to the original query`

    if (depth === 'comprehensive') {
      return basePrompt + `
5. Detailed analysis of each source
6. Cross-reference validation
7. Potential biases or limitations`
    }

    return basePrompt
  }

  private getSynthesisPrompt(depth: string): string {
    const basePrompt = `Synthesize the search results and analysis for the query "{{query}}". Create a coherent summary that combines information from multiple sources.

Input Data:
{{inputs}}

Please provide:
1. Executive summary
2. Key findings with supporting evidence
3. Different perspectives or viewpoints
4. Conclusions and implications`

    if (depth === 'comprehensive') {
      return basePrompt + `
5. Detailed evidence analysis
6. Comparative analysis of sources
7. Recommendations for further research
8. Confidence levels for each finding`
    }

    return basePrompt
  }

  private getGenerationPrompt(depth: string, format: string): string {
    return `Generate a comprehensive research report in ${format} format based on the synthesized information.

Research Data:
{{data}}

The report should include:
1. Executive Summary
2. Introduction and Background
3. Key Findings
4. Analysis and Discussion
5. Conclusions
6. Sources and References

Format the output as clean, well-structured ${format} with appropriate headings, bullet points, and formatting.
${depth === 'comprehensive' ? 'Include detailed analysis, multiple perspectives, and comprehensive coverage of the topic.' : ''}
Ensure the report is professional, informative, and easy to read.`
  }

  private calculateRelevance(source: any, query: string): number {
    // Simple relevance calculation based on keyword matching
    const queryWords = query.toLowerCase().split(' ').filter(word => word.length > 2)
    const sourceText = `${source.title} ${source.snippet}`.toLowerCase()
    
    let matches = 0
    for (const word of queryWords) {
      if (sourceText.includes(word)) {
        matches++
      }
    }
    
    return Math.min(matches / queryWords.length, 1.0)
  }

  public async getResearchHistory(limit: number = 50): Promise<any[]> {
    // TODO: Implement research history storage and retrieval
    return []
  }

  public async saveResearch(result: ResearchResult): Promise<void> {
    // TODO: Implement research result storage
    console.log('Research result saved:', result.id)
  }

  public getAvailableSearchServices(): string[] {
    return this.searchManager.getConfiguredServices()
  }

  public async validateConfiguration(): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = []

    // Check search services
    const searchServices = this.searchManager.getConfiguredServices()
    if (searchServices.length === 0) {
      issues.push('No search services configured')
    }

    // Check AI service
    const aiProviders = this.aiManager.getAvailableProviders()
    if (aiProviders.length === 0) {
      issues.push('No AI services configured')
    }

    return {
      valid: issues.length === 0,
      issues
    }
  }
}