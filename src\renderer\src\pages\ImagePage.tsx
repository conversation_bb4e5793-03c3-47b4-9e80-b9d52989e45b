import React, { useState, useEffect } from 'react'
import { Card, Input, Button, Select, Row, Col, Image, List, Spin, message } from 'antd'
import { PictureOutlined, DownloadOutlined } from '@ant-design/icons'
import { useWebSocket } from '../services/WebSocketService'

const { TextArea } = Input
const { Option } = Select

interface ImageGeneration {
  id: string
  prompt: string
  model: string
  imageUrl?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  created_at: string
}

function ImagePage() {
  const { sendMessage, subscribe, connected } = useWebSocket()
  const [prompt, setPrompt] = useState('')
  const [model, setModel] = useState('dall-e-3')
  const [size, setSize] = useState('1024x1024')
  const [generating, setGenerating] = useState(false)
  const [images, setImages] = useState<ImageGeneration[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadImages()
  }, [])

  useEffect(() => {
    const unsubscribers = [
      subscribe('image:generationStarted', handleGenerationStarted),
      subscribe('image:generationComplete', handleGenerationComplete),
      subscribe('image:error', handleImageError)
    ]

    return () => {
      unsubscribers.forEach(unsub => unsub())
    }
  }, [])

  const loadImages = async () => {
    try {
      setLoading(true)
      const response = await fetch('http://localhost:3001/api/images')
      if (response.ok) {
        const data = await response.json()
        setImages(data)
      }
    } catch (error) {
      console.error('Failed to load images:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleGenerate = () => {
    if (!prompt.trim() || !connected) return

    setGenerating(true)
    sendMessage('image:generate', {
      prompt: prompt.trim(),
      model,
      settings: {
        size,
        quality: 'standard'
      }
    })
  }

  const handleGenerationStarted = (data: any) => {
    const newImage: ImageGeneration = {
      id: data.imageId,
      prompt,
      model,
      status: 'processing',
      created_at: new Date().toISOString()
    }
    setImages(prev => [newImage, ...prev])
    message.info('Image generation started...')
  }

  const handleGenerationComplete = (data: any) => {
    setImages(prev => prev.map(img => 
      img.id === data.imageId 
        ? { ...img, imageUrl: data.imageUrl, status: 'completed' }
        : img
    ))
    setGenerating(false)
    message.success('Image generated successfully!')
  }

  const handleImageError = (data: any) => {
    setGenerating(false)
    message.error('Failed to generate image: ' + data.error)
  }

  const downloadImage = async (imageUrl: string, prompt: string) => {
    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `jimu-ai-${prompt.substring(0, 30).replace(/[^a-zA-Z0-9]/g, '_')}.png`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      message.error('Failed to download image')
    }
  }

  return (
    <div style={{ padding: '24px', height: '100vh', overflow: 'auto' }}>
      <Row gutter={24}>
        <Col span={8}>
          <Card title="Generate Image" style={{ height: 'fit-content' }}>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                Prompt
              </label>
              <TextArea
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Describe the image you want to generate..."
                rows={4}
                maxLength={1000}
                showCount
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                Model
              </label>
              <Select
                value={model}
                onChange={setModel}
                style={{ width: '100%' }}
              >
                <Option value="dall-e-3">DALL-E 3</Option>
                <Option value="dall-e-2">DALL-E 2</Option>
              </Select>
            </div>

            <div style={{ marginBottom: '24px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                Size
              </label>
              <Select
                value={size}
                onChange={setSize}
                style={{ width: '100%' }}
              >
                <Option value="1024x1024">1024x1024 (Square)</Option>
                <Option value="1024x1792">1024x1792 (Portrait)</Option>
                <Option value="1792x1024">1792x1024 (Landscape)</Option>
              </Select>
            </div>

            <Button
              type="primary"
              icon={<PictureOutlined />}
              onClick={handleGenerate}
              loading={generating}
              disabled={!prompt.trim() || !connected}
              style={{ width: '100%' }}
              size="large"
            >
              {generating ? 'Generating...' : 'Generate Image'}
            </Button>

            {!connected && (
              <div style={{ marginTop: '16px', color: '#ff4d4f', textAlign: 'center' }}>
                Disconnected from server
              </div>
            )}
          </Card>
        </Col>

        <Col span={16}>
          <Card 
            title="Generated Images" 
            style={{ height: 'calc(100vh - 48px)' }}
            bodyStyle={{ height: 'calc(100vh - 120px)', overflow: 'auto' }}
          >
            {loading ? (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
              </div>
            ) : (
              <List
                grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
                dataSource={images}
                renderItem={(item) => (
                  <List.Item>
                    <Card
                      hoverable
                      style={{ width: '100%' }}
                      cover={
                        item.status === 'completed' && item.imageUrl ? (
                          <Image
                            src={item.imageUrl}
                            alt={item.prompt}
                            style={{ height: '200px', objectFit: 'cover' }}
                            preview={{
                              mask: (
                                <div style={{ textAlign: 'center' }}>
                                  <PictureOutlined style={{ fontSize: '24px' }} />
                                  <div>Preview</div>
                                </div>
                              )
                            }}
                          />
                        ) : (
                          <div style={{ 
                            height: '200px', 
                            display: 'flex', 
                            alignItems: 'center', 
                            justifyContent: 'center',
                            backgroundColor: '#f5f5f5'
                          }}>
                            {item.status === 'processing' ? (
                              <Spin size="large" />
                            ) : (
                              <PictureOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                            )}
                          </div>
                        )
                      }
                      actions={
                        item.status === 'completed' && item.imageUrl ? [
                          <Button
                            type="text"
                            icon={<DownloadOutlined />}
                            onClick={() => downloadImage(item.imageUrl!, item.prompt)}
                          >
                            Download
                          </Button>
                        ] : []
                      }
                    >
                      <Card.Meta
                        title={
                          <div style={{ 
                            fontSize: '12px', 
                            color: item.status === 'completed' ? '#52c41a' : '#1890ff'
                          }}>
                            {item.status.toUpperCase()}
                          </div>
                        }
                        description={
                          <div>
                            <div style={{ 
                              fontSize: '14px', 
                              marginBottom: '8px',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden'
                            }}>
                              {item.prompt}
                            </div>
                            <div style={{ fontSize: '12px', color: '#999' }}>
                              {item.model} • {new Date(item.created_at).toLocaleDateString()}
                            </div>
                          </div>
                        }
                      />
                    </Card>
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default ImagePage