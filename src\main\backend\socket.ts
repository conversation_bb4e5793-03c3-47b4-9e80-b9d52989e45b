import { Server as SocketIOServer, Socket } from 'socket.io'
import { DatabaseService } from '../database/DatabaseService'
import { ConfigService } from '../services/ConfigService'
import { ConversationService } from '../services/ConversationService'
import { AIManager } from '../services/ai/AIManager'

export function setupSocketHandlers(io: SocketIOServer): void {
  const db = DatabaseService.getInstance()
  const config = ConfigService.getInstance()
  const conversationService = ConversationService.getInstance()
  const aiManager = AIManager.getInstance()

  io.on('connection', (socket: Socket) => {
    console.log(`Client connected: ${socket.id}`)

    // Chat message handling
    socket.on('chat:message', async (data) => {
      try {
        const { conversationId, message, role = 'user', userId = 1 } = data

        // Save user message
        const messageId = await conversationService.saveMessage(conversationId, role, message)

        // Emit message saved confirmation
        socket.emit('chat:messageSaved', { messageId, conversationId })

        // Get conversation history for context
        const messages = await conversationService.getMessages(conversationId, 20)
        const chatMessages = messages.map(msg => ({
          role: msg.role as 'user' | 'assistant' | 'system',
          content: msg.content
        }))

        // Generate AI response using streaming
        const assistantMessageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        let fullResponse = ''

        try {
          await aiManager.streamCompletion({
            messages: chatMessages,
            onChunk: (chunk: string) => {
              fullResponse += chunk
              socket.emit('chat:streamChunk', {
                messageId: assistantMessageId,
                conversationId,
                chunk,
                currentText: fullResponse,
                isComplete: false
              })
            },
            onComplete: async (response: string) => {
              // Save assistant message
              await conversationService.saveMessage(conversationId, 'assistant', response)
              
              socket.emit('chat:streamChunk', {
                messageId: assistantMessageId,
                conversationId,
                chunk: '',
                currentText: response,
                isComplete: true
              })
            },
            onError: (error: Error) => {
              console.error('AI streaming error:', error)
              socket.emit('chat:error', { error: error.message })
            }
          })
        } catch (error) {
          console.error('AI service error:', error)
          // Fallback to mock response if AI service fails
          const fallbackResponse = `I'm sorry, I'm having trouble connecting to the AI service right now. Please check your configuration and try again.`
          await conversationService.saveMessage(conversationId, 'assistant', fallbackResponse)
          
          socket.emit('chat:streamChunk', {
            messageId: assistantMessageId,
            conversationId,
            chunk: fallbackResponse,
            currentText: fallbackResponse,
            isComplete: true
          })
        }

      } catch (error) {
        console.error('Error handling chat message:', error)
        socket.emit('chat:error', { error: 'Failed to process message' })
      }
    })

    // Configuration change notifications
    socket.on('config:subscribe', (keys: string[]) => {
      keys.forEach(key => {
        const unwatch = config.watch(key, (value) => {
          socket.emit('config:changed', { key, value })
        })
        
        // Store unwatch function for cleanup
        socket.data.configWatchers = socket.data.configWatchers || []
        socket.data.configWatchers.push(unwatch)
      })
    })

    // Image generation handling
    socket.on('image:generate', async (data) => {
      try {
        const { prompt, model, settings } = data
        const imageId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        // Save generation request
        await db.run(
          'INSERT INTO image_generations (id, user_id, prompt, model, metadata) VALUES (?, ?, ?, ?, ?)',
          [imageId, 1, prompt, model, JSON.stringify(settings)]
        )

        // Emit generation started
        socket.emit('image:generationStarted', { imageId, status: 'processing' })

        // TODO: Implement actual image generation
        // For now, simulate processing
        setTimeout(async () => {
          const mockImageUrl = `https://via.placeholder.com/1024x1024?text=${encodeURIComponent(prompt.substring(0, 20))}`
          
          await db.run(
            'UPDATE image_generations SET image_url = ? WHERE id = ?',
            [mockImageUrl, imageId]
          )

          socket.emit('image:generationComplete', {
            imageId,
            imageUrl: mockImageUrl,
            status: 'completed'
          })
        }, 3000)

      } catch (error) {
        console.error('Error handling image generation:', error)
        socket.emit('image:error', { error: 'Failed to generate image' })
      }
    })

    // Plugin management
    socket.on('plugin:execute', async (data) => {
      try {
        const { pluginId, action, params } = data
        
        // TODO: Implement plugin execution
        socket.emit('plugin:result', {
          pluginId,
          action,
          result: { success: true, message: 'Plugin executed successfully' }
        })
      } catch (error) {
        console.error('Error executing plugin:', error)
        socket.emit('plugin:error', { error: 'Failed to execute plugin' })
      }
    })

    // Research agent handling
    socket.on('research:start', async (data) => {
      try {
        const { query, options } = data
        const researchId = `research_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        socket.emit('research:started', { researchId, status: 'searching' })

        // TODO: Implement research workflow
        // Simulate research process
        const steps = ['Searching...', 'Analyzing...', 'Synthesizing...', 'Generating report...']
        
        for (let i = 0; i < steps.length; i++) {
          socket.emit('research:progress', {
            researchId,
            step: i + 1,
            totalSteps: steps.length,
            message: steps[i]
          })
          await new Promise(resolve => setTimeout(resolve, 2000))
        }

        socket.emit('research:complete', {
          researchId,
          result: {
            query,
            summary: `Research completed for: "${query}"`,
            sources: [],
            report: 'This is a mock research report.'
          }
        })

      } catch (error) {
        console.error('Error handling research:', error)
        socket.emit('research:error', { error: 'Failed to complete research' })
      }
    })

    // Cleanup on disconnect
    socket.on('disconnect', () => {
      console.log(`Client disconnected: ${socket.id}`)
      
      // Clean up config watchers
      if (socket.data.configWatchers) {
        socket.data.configWatchers.forEach((unwatch: Function) => unwatch())
      }
    })
  })
}