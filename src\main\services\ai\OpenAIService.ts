import OpenAI from 'openai'
import { AIService, ChatCompletionParams, StreamCompletionParams, ChatCompletionResponse, AIServiceConfig } from './AIService'

export class OpenAIService extends AIService {
  private client: OpenAI

  constructor(config: AIServiceConfig) {
    super(config, 'OpenAI')
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL
    })
  }

  async chatCompletion(params: ChatCompletionParams): Promise<ChatCompletionResponse> {
    try {
      const response = await this.client.chat.completions.create({
        model: params.model || this.config.model || 'gpt-3.5-turbo',
        messages: params.messages,
        temperature: params.temperature ?? this.config.temperature ?? 0.7,
        max_tokens: params.maxTokens ?? this.config.maxTokens ?? 2000,
        stream: false
      })

      const choice = response.choices[0]
      if (!choice?.message?.content) {
        throw new Error('No response content from OpenAI')
      }

      return {
        content: choice.message.content,
        model: response.model,
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens
        } : undefined
      }
    } catch (error) {
      throw this.handleError(error, 'Chat completion')
    }
  }

  async streamCompletion(params: StreamCompletionParams): Promise<void> {
    try {
      const stream = await this.client.chat.completions.create({
        model: params.model || this.config.model || 'gpt-3.5-turbo',
        messages: params.messages,
        temperature: params.temperature ?? this.config.temperature ?? 0.7,
        max_tokens: params.maxTokens ?? this.config.maxTokens ?? 2000,
        stream: true
      })

      let fullResponse = ''

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content
        if (content) {
          fullResponse += content
          params.onChunk?.(content)
        }
      }

      params.onComplete?.(fullResponse)
    } catch (error) {
      const handledError = this.handleError(error, 'Stream completion')
      params.onError?.(handledError)
      throw handledError
    }
  }

  async getModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list()
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort()
    } catch (error) {
      throw this.handleError(error, 'Get models')
    }
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.client.models.list()
      return true
    } catch (error) {
      console.error('OpenAI config validation failed:', error)
      return false
    }
  }

  updateConfig(newConfig: Partial<AIServiceConfig>): void {
    super.updateConfig(newConfig)
    this.client = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseURL
    })
  }
}