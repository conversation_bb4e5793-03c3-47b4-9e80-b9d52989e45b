import { promises as fs } from 'fs'
import { join, extname, basename } from 'path'
import { app } from 'electron'
import { DatabaseService } from '../database/DatabaseService'
import crypto from 'crypto'

export interface FileData {
  name: string
  content: Buffer | string
  mimeType: string
  size: number
  userId?: number
}

export interface FileRecord {
  id: string
  name: string
  originalName: string
  path: string
  mimeType: string
  size: number
  userId: number
  hash: string
  created_at: string
  updated_at: string
}

export interface FileUploadOptions {
  maxSize?: number
  allowedTypes?: string[]
  generateThumbnail?: boolean
}

export class FileService {
  private static instance: FileService
  private db: DatabaseService
  private filesDir: string
  private maxFileSize: number = 100 * 1024 * 1024 // 100MB default
  private allowedMimeTypes: Set<string>

  private constructor() {
    this.db = DatabaseService.getInstance()
    this.filesDir = join(app.getPath('userData'), 'files')
    this.allowedMimeTypes = new Set([
      // Images
      'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
      // Documents
      'application/pdf', 'text/plain', 'text/markdown',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      // Archives
      'application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed',
      // Code
      'text/javascript', 'text/typescript', 'text/css', 'text/html', 'application/json',
      // Audio
      'audio/mpeg', 'audio/wav', 'audio/ogg',
      // Video
      'video/mp4', 'video/webm', 'video/ogg'
    ])
    
    this.ensureFilesDirectory()
  }

  public static getInstance(): FileService {
    if (!FileService.instance) {
      FileService.instance = new FileService()
    }
    return FileService.instance
  }

  private async ensureFilesDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.filesDir, { recursive: true })
      
      // Create subdirectories
      const subdirs = ['images', 'documents', 'archives', 'temp']
      for (const subdir of subdirs) {
        await fs.mkdir(join(this.filesDir, subdir), { recursive: true })
      }
    } catch (error) {
      console.error('Failed to create files directory:', error)
    }
  }

  public async saveFile(fileData: FileData, options: FileUploadOptions = {}): Promise<FileRecord> {
    try {
      // Validate file
      this.validateFile(fileData, options)

      // Generate file ID and hash
      const fileId = this.generateFileId()
      const content = Buffer.isBuffer(fileData.content) ? fileData.content : Buffer.from(fileData.content)
      const hash = this.calculateHash(content)

      // Check for duplicates
      const existingFile = await this.findFileByHash(hash)
      if (existingFile) {
        console.log('File already exists, returning existing record')
        return existingFile
      }

      // Determine file category and path
      const category = this.getFileCategory(fileData.mimeType)
      const extension = extname(fileData.name) || this.getExtensionFromMimeType(fileData.mimeType)
      const fileName = `${fileId}${extension}`
      const filePath = join(this.filesDir, category, fileName)

      // Save file to disk
      await fs.writeFile(filePath, content)

      // Create file record
      const fileRecord: FileRecord = {
        id: fileId,
        name: fileName,
        originalName: fileData.name,
        path: filePath,
        mimeType: fileData.mimeType,
        size: fileData.size,
        userId: fileData.userId || 1,
        hash,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // Save to database
      await this.saveFileRecord(fileRecord)

      return fileRecord
    } catch (error) {
      console.error('Failed to save file:', error)
      throw error
    }
  }

  private validateFile(fileData: FileData, options: FileUploadOptions): void {
    // Check file size
    const maxSize = options.maxSize || this.maxFileSize
    if (fileData.size > maxSize) {
      throw new Error(`File size exceeds maximum allowed size of ${maxSize} bytes`)
    }

    // Check file type
    const allowedTypes = options.allowedTypes ? new Set(options.allowedTypes) : this.allowedMimeTypes
    if (!allowedTypes.has(fileData.mimeType)) {
      throw new Error(`File type '${fileData.mimeType}' is not allowed`)
    }

    // Check file name
    if (!fileData.name || fileData.name.trim().length === 0) {
      throw new Error('File name is required')
    }

    // Check for malicious file names
    if (fileData.name.includes('..') || fileData.name.includes('/') || fileData.name.includes('\\')) {
      throw new Error('Invalid file name')
    }
  }

  private generateFileId(): string {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private calculateHash(content: Buffer): string {
    return crypto.createHash('sha256').update(content).digest('hex')
  }

  private getFileCategory(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'images'
    if (mimeType.startsWith('audio/') || mimeType.startsWith('video/')) return 'media'
    if (mimeType.includes('zip') || mimeType.includes('rar') || mimeType.includes('7z')) return 'archives'
    return 'documents'
  }

  private getExtensionFromMimeType(mimeType: string): string {
    const mimeToExt: { [key: string]: string } = {
      'image/jpeg': '.jpg',
      'image/png': '.png',
      'image/gif': '.gif',
      'image/webp': '.webp',
      'image/svg+xml': '.svg',
      'application/pdf': '.pdf',
      'text/plain': '.txt',
      'text/markdown': '.md',
      'application/json': '.json',
      'text/javascript': '.js',
      'text/typescript': '.ts',
      'text/css': '.css',
      'text/html': '.html'
    }
    return mimeToExt[mimeType] || ''
  }

  private async findFileByHash(hash: string): Promise<FileRecord | null> {
    try {
      const result = await this.db.get(
        'SELECT * FROM files WHERE hash = ?',
        [hash]
      )
      return result || null
    } catch (error) {
      console.error('Failed to find file by hash:', error)
      return null
    }
  }

  private async saveFileRecord(fileRecord: FileRecord): Promise<void> {
    try {
      // First, ensure the files table exists
      await this.db.run(`
        CREATE TABLE IF NOT EXISTS files (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          original_name TEXT NOT NULL,
          path TEXT NOT NULL,
          mime_type TEXT NOT NULL,
          size INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          hash TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `)

      await this.db.run(`
        INSERT INTO files (id, name, original_name, path, mime_type, size, user_id, hash)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        fileRecord.id,
        fileRecord.name,
        fileRecord.originalName,
        fileRecord.path,
        fileRecord.mimeType,
        fileRecord.size,
        fileRecord.userId,
        fileRecord.hash
      ])
    } catch (error) {
      console.error('Failed to save file record:', error)
      throw error
    }
  }

  public async getFile(fileId: string): Promise<FileRecord | null> {
    try {
      const result = await this.db.get(
        'SELECT * FROM files WHERE id = ?',
        [fileId]
      )
      return result || null
    } catch (error) {
      console.error('Failed to get file:', error)
      return null
    }
  }

  public async getFileContent(fileId: string): Promise<Buffer | null> {
    try {
      const fileRecord = await this.getFile(fileId)
      if (!fileRecord) return null

      const content = await fs.readFile(fileRecord.path)
      return content
    } catch (error) {
      console.error('Failed to get file content:', error)
      return null
    }
  }

  public async getFilesByUser(userId: number, limit: number = 50, offset: number = 0): Promise<FileRecord[]> {
    try {
      const results = await this.db.all(`
        SELECT * FROM files 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `, [userId, limit, offset])

      return results
    } catch (error) {
      console.error('Failed to get files by user:', error)
      return []
    }
  }

  public async deleteFile(fileId: string): Promise<boolean> {
    try {
      const fileRecord = await this.getFile(fileId)
      if (!fileRecord) return false

      // Delete file from disk
      try {
        await fs.unlink(fileRecord.path)
      } catch (error) {
        console.warn('Failed to delete file from disk:', error)
      }

      // Delete from database
      const result = await this.db.run('DELETE FROM files WHERE id = ?', [fileId])
      return result.changes > 0
    } catch (error) {
      console.error('Failed to delete file:', error)
      return false
    }
  }

  public async getFileStats(userId?: number): Promise<{
    totalFiles: number
    totalSize: number
    filesByType: { [type: string]: number }
  }> {
    try {
      const whereClause = userId ? 'WHERE user_id = ?' : ''
      const params = userId ? [userId] : []

      const totalResult = await this.db.get(`
        SELECT COUNT(*) as count, SUM(size) as size 
        FROM files ${whereClause}
      `, params)

      const typeResults = await this.db.all(`
        SELECT mime_type, COUNT(*) as count 
        FROM files ${whereClause}
        GROUP BY mime_type
      `, params)

      const filesByType: { [type: string]: number } = {}
      for (const result of typeResults) {
        filesByType[result.mime_type] = result.count
      }

      return {
        totalFiles: totalResult?.count || 0,
        totalSize: totalResult?.size || 0,
        filesByType
      }
    } catch (error) {
      console.error('Failed to get file stats:', error)
      return { totalFiles: 0, totalSize: 0, filesByType: {} }
    }
  }

  public async cleanupOrphanedFiles(): Promise<number> {
    try {
      // Find files in database that don't exist on disk
      const allFiles = await this.db.all('SELECT id, path FROM files')
      let cleanedCount = 0

      for (const file of allFiles) {
        try {
          await fs.access(file.path)
        } catch {
          // File doesn't exist on disk, remove from database
          await this.db.run('DELETE FROM files WHERE id = ?', [file.id])
          cleanedCount++
        }
      }

      return cleanedCount
    } catch (error) {
      console.error('Failed to cleanup orphaned files:', error)
      return 0
    }
  }

  public getAllowedMimeTypes(): string[] {
    return Array.from(this.allowedMimeTypes)
  }

  public setMaxFileSize(size: number): void {
    this.maxFileSize = size
  }

  public getMaxFileSize(): number {
    return this.maxFileSize
  }
}