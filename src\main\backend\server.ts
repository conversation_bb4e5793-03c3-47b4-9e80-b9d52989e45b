import express from 'express'
import { createServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import cors from 'cors'
import helmet from 'helmet'
import { DatabaseService } from '../database/DatabaseService'
import { ConfigService } from '../services/ConfigService'
import { setupRoutes } from './routes'
import { setupSocketHandlers } from './socket'

export class BackendServer {
  private app: express.Application
  private server: any
  private io: SocketIOServer
  private port: number = 3001

  constructor() {
    this.app = express()
    this.server = createServer(this.app)
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    })
    
    this.setupMiddleware()
    this.setupRoutes()
    this.setupSocketHandlers()
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false // Disable for Electron
    }))
    
    // CORS
    this.app.use(cors())
    
    // Body parsing
    this.app.use(express.json({ limit: '50mb' }))
    this.app.use(express.urlencoded({ extended: true, limit: '50mb' }))
    
    // Static files
    this.app.use('/static', express.static('public'))
  }

  private setupRoutes(): void {
    setupRoutes(this.app)
  }

  private setupSocketHandlers(): void {
    setupSocketHandlers(this.io)
  }

  public async start(): Promise<void> {
    try {
      // Initialize database
      await DatabaseService.getInstance().initialize()
      
      // Initialize config service
      await ConfigService.getInstance().initialize()
      
      // Start server
      this.server.listen(this.port, () => {
        console.log(`🚀 Backend server running on port ${this.port}`)
      })
    } catch (error) {
      console.error('Failed to start backend server:', error)
      throw error
    }
  }

  public async stop(): Promise<void> {
    if (this.server) {
      this.server.close()
    }
  }
}

let serverInstance: BackendServer | null = null

export async function startBackendServer(): Promise<void> {
  if (!serverInstance) {
    serverInstance = new BackendServer()
    await serverInstance.start()
  }
}

export async function stopBackendServer(): Promise<void> {
  if (serverInstance) {
    await serverInstance.stop()
    serverInstance = null
  }
}