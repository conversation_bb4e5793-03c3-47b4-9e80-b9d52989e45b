export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export interface ChatCompletionParams {
  messages: ChatMessage[]
  model?: string
  temperature?: number
  maxTokens?: number
  stream?: boolean
}

export interface StreamCompletionParams extends ChatCompletionParams {
  onChunk?: (chunk: string) => void
  onComplete?: (fullResponse: string) => void
  onError?: (error: Error) => void
}

export interface ChatCompletionResponse {
  content: string
  model: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}

export interface AIServiceConfig {
  apiKey: string
  baseURL?: string
  model?: string
  temperature?: number
  maxTokens?: number
}

export abstract class AIService {
  protected config: AIServiceConfig
  protected serviceName: string

  constructor(config: AIServiceConfig, serviceName: string) {
    this.config = config
    this.serviceName = serviceName
  }

  abstract chatCompletion(params: ChatCompletionParams): Promise<ChatCompletionResponse>
  abstract streamCompletion(params: StreamCompletionParams): Promise<void>
  abstract getModels(): Promise<string[]>
  abstract validateConfig(): Promise<boolean>

  protected handleError(error: any, context: string): Error {
    console.error(`[${this.serviceName}] ${context}:`, error)
    
    if (error.response?.status === 401) {
      return new Error(`Invalid API key for ${this.serviceName}`)
    } else if (error.response?.status === 429) {
      return new Error(`Rate limit exceeded for ${this.serviceName}`)
    } else if (error.response?.status === 400) {
      return new Error(`Invalid request to ${this.serviceName}: ${error.response?.data?.error?.message || error.message}`)
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return new Error(`Network error connecting to ${this.serviceName}`)
    }
    
    return new Error(`${this.serviceName} error: ${error.message || 'Unknown error'}`)
  }

  public getServiceName(): string {
    return this.serviceName
  }

  public updateConfig(newConfig: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }
}